import 'dart:io';

import 'package:google_mobile_ads/google_mobile_ads.dart';



class AdsHelper {
 // // static bool _testMood = false;
 //  static String getBunnerAd() {
 //    // if (_testMood) {
 //    //   return AdmobBanner.testAdUnitId;
 //    // }
 //     if (Platform.isAndroid) {
 //      return "ca-app-pub-2342851934476929/8822850492";
 //    } else if (Platform.isIOS) {
 //      return "ca-app-pub-2342851934476929/9636969213";
 //    } else {
 //      return "UnSuported platForm";
 //    }
 //  }
 //
 //  static String getinitAd() {
 //    // if (_testMood) {
 //    //   return AdmobInterstitial.testAdUnitId;
 //    // }
 //    if (Platform.isAndroid) {
 //      return "-----------------------------------";
 //    }
 //    else if (Platform.isIOS) {
 //      return "-----------------------------------";
 //    } else {
 //      return "UnSuported platForm";
 //    }
 //  }
 //
 //  static String rewarderAds() {
 //    // if (_testMood) {
 //    //   return AdmobReward.testAdUnitId;
 //    // }
 //    if (Platform.isAndroid) {
 //      return "ca-app-pub-2342851934476929/3015974131";
 //    } else if (Platform.isIOS) {
 //      return "-----------------------------------";
 //    } else {
 //      return "UnSuported platForm";
 //    }
 //  }
  // static String NativeAd() {
  //   if (_testMood) {
  //     return NativeAd();
  //   } else if (Platform.isAndroid) {
  //     return "-----------------------------------";
  //   } else if (Platform.isIOS) {
  //     return "-----------------------------------";
  //   } else {
  //     return "UnSuported platForm";
  //   }
  // }
}
// ignore_for_file: avoid_print, prefer_const_constructors


//
// InterstitialAd? interstitialAd;
// RewardedInterstitialAd? rewardedInterstitialAd;
// bool? adCompleted;
// bool? isHomeBannerLoaded = false;

class AdmobService {
  // static Initialize() {
  //   MobileAds.instance.initialize();
  // }


  //
  // static createRewardedInterstitialAd(context) {
  //   RewardedInterstitialAd.load(
  //       adUnitId: AdsHelper.rewarderAds(),
  //       request: AdRequest(),
  //       rewardedInterstitialAdLoadCallback: RewardedInterstitialAdLoadCallback(
  //           onAdLoaded: (RewardedInterstitialAd ad) {
  //             print('$ad loaded.');
  //             rewardedInterstitialAd = ad;
  //             showRewardedInterstitialAd(context);
  //           }, onAdFailedToLoad: (LoadAdError error) {
  //         print('RewardedInterstitialAd failed to load: $error');
  //       }));
  // }

  // static showRewardedInterstitialAd(context) async {
  //   rewardedInterstitialAd!.fullScreenContentCallback =
  //       FullScreenContentCallback(
  //         onAdShowedFullScreenContent: (RewardedInterstitialAd ad) {
  //           print('$ad onAdShowedFullScreenContent.');
  //         },
  //         onAdDismissedFullScreenContent: (RewardedInterstitialAd ad) {
  //           print('$ad onAdDismissedFullScreenContent.');
  //           ad.dispose();
  //         },
  //         onAdFailedToShowFullScreenContent:
  //             (RewardedInterstitialAd ad, AdError error) {
  //           print('$ad onAdFailedToShowFullScreenContent: $error');
  //
  //           ad.dispose();
  //         },
  //         onAdImpression: (RewardedInterstitialAd ad) =>
  //             print('$ad impression occurred.'),
  //       );
  //   rewardedInterstitialAd!
  //       .show(onUserEarnedReward: (ad, RewardItem rewardItem) {});
  // }

  //////////////////////////////////////////////////////////////////////////////

  //
  // static Future createInterstitialAd(context, String number) async {
  //   await InterstitialAd.load(
  //       adUnitId: AdsHelper.getinitAd(),
  //       request: AdRequest(),
  //       adLoadCallback: InterstitialAdLoadCallback(
  //         onAdLoaded: (InterstitialAd ad) async {
  //           print('$ad has loaded');
  //           interstitialAd = ad;
  //           await showInterstitialAd(context, number);
  //         },
  //         onAdFailedToLoad: (LoadAdError error) {
  //           print('InterstitialAd failed to load: $error');
  //
  //         },
  //       ));
  // }

  // static Future showInterstitialAd(context, number) async {
  //   interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
  //     onAdShowedFullScreenContent: (InterstitialAd ad) {
  //       print('$ad onAdShowedFullScreenContent.');
  //     },
  //     onAdDismissedFullScreenContent: (InterstitialAd ad) async {
  //       print('$ad onAdDismissedFullScreenContent.');
  //
  //       ad.dispose();
  //     },
  //     onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
  //       print('$ad onAdFailedToShowFullScreenContent: $error');
  //
  //
  //       ad.dispose();
  //     },
  //     onAdImpression: (InterstitialAd ad) => print('$ad impression occurred.'),
  //   );
  //   await interstitialAd!.show();
  // }

  //////////////////////////////////////////////////////////////////////////////

  // static late final BannerAd homeBannerAd = BannerAd(
  //   adUnitId: AdsHelper.getBunnerAd(),
  //   size: AdSize.largeBanner,
  //   request: AdRequest(),
  //   listener: BannerAdListener(
  //     onAdLoaded: ((ad) {
  //       isHomeBannerLoaded = true;
  //       print('$ad banner loaded');
  //     }),
  //     onAdFailedToLoad: (Ad ad, LoadAdError error) {
  //       ad.dispose();
  //       isHomeBannerLoaded = false;
  //     },
  //     onAdClosed: (Ad ad) {
  //       isHomeBannerLoaded = false;
  //
  //       ad.dispose();
  //     },
  //   ),
  // );

  // static late final BannerAd homeBanner2Ad = BannerAd(
  //   adUnitId: AdsHelper.getBunnerAd(),
  //   size: AdSize.mediumRectangle,
  //   request: AdRequest(),
  //   listener: BannerAdListener(
  //     onAdLoaded: ((ad) {
  //       isHomeBannerLoaded = true;
  //       print('$ad banner loaded');
  //     }),
  //     onAdFailedToLoad: (Ad ad, LoadAdError error) {
  //       ad.dispose();
  //       isHomeBannerLoaded = false;
  //     },
  //     onAdClosed: (Ad ad) {
  //       isHomeBannerLoaded = false;
  //
  //       ad.dispose();
  //     },
  //   ),
  // );

  // static late final BannerAd mapBanner = BannerAd(
  //   adUnitId: AdsHelper.getBunnerAd(),
  //   size: AdSize.mediumRectangle,
  //   request: AdRequest(),
  //   listener: BannerAdListener(
  //     onAdLoaded: ((ad) {
  //       isHomeBannerLoaded = true;
  //       print('$ad banner loaded');
  //     }),
  //     onAdFailedToLoad: (Ad ad, LoadAdError error) {
  //       ad.dispose();
  //       isHomeBannerLoaded = false;
  //     },
  //     onAdClosed: (Ad ad) {
  //       isHomeBannerLoaded = false;
  //
  //       ad.dispose();
  //     },
  //   ),
  // );
}
