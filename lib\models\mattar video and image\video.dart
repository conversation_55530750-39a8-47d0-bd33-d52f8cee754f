import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:mattar/component/component.dart';
import 'package:mattar/component/constants.dart';
import 'package:mattar/models/ads/ads_helper.dart';
import 'package:mattar/models/mattar%20video%20and%20image/cubit/cubit/video_cubit.dart';
import 'package:mattar/network/local/shared_pref.dart';
import 'package:path/path.dart' as p;
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';

import '../ads/admob_service.dart';

class Videos extends StatelessWidget {
  const Videos({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => VideoCubit()
        ..getVideoPostes()
        ..getPrivateAds(),
      child: BlocConsumer<VideoCubit, VideoState>(
        listener: (context, state) {},
        builder: (context, state) {
          PageController controller = PageController();
          var cubit = VideoCubit.caller(context);
          List ind = [1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49];
          Future<void> share(String title, String text, String url,
              String chooserTitle) async {
            await Share.share(
                '$title\n$text\n$url',
                subject: chooserTitle);
          }
          if(state is GetVideoLoadingState){
            const Center(child: CircularProgressIndicator());
          }
          return PageView.builder(
            scrollDirection: Axis.vertical,
            itemCount: cubit.posts.length,
            controller: controller,
            itemBuilder: (ctx, index) {
              final h =  MediaQuery.of(context).size.height.toInt() - AppBar().preferredSize.height - kBottomNavigationBarHeight - MediaQueryData.fromWindow(window).padding.top - kToolbarHeight ;
              print("ss ${kToolbarHeight}");
              if (ind.contains(index) == false) {}
              final extension = p.extension(cubit.posts[index].media);
              return ind.contains(index) &&
                  CacheHelper.getData(key: "subscibtion")!= true  ||
                      ind.contains(index) &&
                          CacheHelper.getData(key: "subscibtion")!= true ||
                      ind.contains(index) &&
                          CacheHelper.getData(key: "subscibtion")!= true
                  ? PageView.builder(
                      itemCount: 1,
                      itemBuilder: (c, n) {
                        return cubit.adsModel.isNotEmpty && index == 1
                            ? Column(
                                children: [
                                  const SizedBox(height: 20),
                                  Text(cubit.adsModel[n].title,
                                      style: Theme.of(context)
                                          .textTheme
                                          .displayLarge
                                          ?.copyWith(color: Colors.grey[600])),
                                  Expanded(
                                    child: Container(
                                      color: Colors.white,
                                      margin: const EdgeInsets.all(10),
                                      child: Image.network(
                                          "https://admin.rain-app.com/storage/ads/${cubit.adsModel[n].media}",
                                          errorBuilder: (BuildContext context,
                                              Object exception,
                                              StackTrace? stackTrace) {
                                        return const Text(
                                            'Your error widget...');
                                      }),
                                    ),
                                  ),
                                  defaultButton(
                                      onPressed: () async {
                                        try {
                                          await canLaunch(
                                                  cubit.adsModel[n].redirect)
                                              ? await launch(
                                                  cubit.adsModel[n].redirect)
                                              : throw "could not fiend";
                                        } catch (e) {
                                          return;
                                        }
                                      },
                                      textButton: "اذهب",
                                      width: 150,
                                      radius: 15),
                                  const SizedBox(
                                    height: 10,
                                  )
                                ],
                              )
                            : SizedBox(
                              height: h,
                              width: MediaQuery.of(context).size.width,
                              child: AdWidget(
                                ad: BannerAd(
                                  adUnitId: AdmobServiceNew.adUnitIdBanner,
                                  size: AdSize(width: MediaQuery.of(context).size.width.toInt(), height: h.toInt()),
                                  request: AdRequest(),
                                  listener:
                                  BannerAdListener(
                                    onAdLoaded: (Ad ad) =>
                                        print('Ad loaded.'),
                                    onAdFailedToLoad:
                                        (Ad ad, LoadAdError
                                    error) {
                                      // Dispose the ad here to free resources.
                                      ad.dispose();
                                      print(
                                          'Ad failed to load: $error');
                                    },
                                    onAdOpened:
                                        (Ad ad) =>
                                        print('Ad opened.'),
                                    onAdClosed:
                                        (Ad ad) =>
                                        print('Ad closed.'),
                                    onAdImpression:
                                        (Ad ad) => print(
                                        'Ad impression.'),
                                  ),
                                )..load(),
                              ),
                            );
                      })
                  : Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      color: Colors.black,
                      child: Column(
                        children: [
                          SizedBox(
                            height: 80,
                            child: AdWidget(
                              ad: BannerAd(
                                adUnitId: AdmobServiceNew.adUnitIdBanner,
                                size: AdSize(width: MediaQuery.of(context).size.width.toInt(), height: 80),
                                request: AdRequest(),
                                listener:
                                BannerAdListener(
                                  onAdLoaded: (Ad ad) =>
                                      print('Ad loaded.'),
                                  onAdFailedToLoad:
                                      (Ad ad, LoadAdError
                                  error) {
                                    // Dispose the ad here to free resources.
                                    ad.dispose();
                                    print(
                                        'Ad failed to load: $error');
                                  },
                                  onAdOpened:
                                      (Ad ad) =>
                                      print('Ad opened.'),
                                  onAdClosed:
                                      (Ad ad) =>
                                      print('Ad closed.'),
                                  onAdImpression:
                                      (Ad ad) => print(
                                      'Ad impression.'),
                                ),
                              )..load(),
                            ),
                          ),
                          Expanded(
                            child: Stack(
                              fit: StackFit.expand,
                              children: [
                                extension == ".mp4"
                                    ? SizedBox(
                                        width: MediaQuery.of(context).size.width,
                                        child: PlayVideo(
                                          "https://admin.rain-app.com/storage/weather-shots/${cubit.posts[index].media}",
                                        ),
                                      )
                                    : SizedBox(
                                        width: MediaQuery.of(context).size.width,
                                        child: Image.network(
                                          "https://admin.rain-app.com/storage/weather-shots/${cubit.posts[index].media}",
                                          fit: BoxFit.fitWidth,
                                        ),
                                      ),
                                Positioned(
                                    bottom: 70,
                                    right: 8,
                                    child: Column(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: whiteColor,
                                            borderRadius: BorderRadius.circular(12),
                                            // boxShadow: [shadow()],
                                          ),
                                          child: defaultIconButton(
                                              onPressed: () {
                                                if (CacheHelper.getData(
                                                        key: "token") ==
                                                    null) {
                                                  LogInDialog(context);
                                                } else {
                                                  Navigator.of(context)
                                                      .pushReplacementNamed(
                                                          "addvideo");
                                                }
                                              },
                                              icon: Icons.add,
                                              color: Colors.black,
                                              iconSize: 30),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: whiteColor,
                                            borderRadius: BorderRadius.circular(12),
                                            boxShadow: [shadow()],
                                          ),
                                          child: defaultIconButton(
                                              onPressed: () {
                                                showDialog(
                                                    context: context,
                                                    useRootNavigator: true,
                                                    builder: (BuildContext ctx) {
                                                      return AlertDialog(
                                                        content: SizedBox(
                                                          height: 170,
                                                          child:
                                                              SingleChildScrollView(
                                                            child: Column(
                                                              children: [
                                                                rowAndIcon(
                                                                    icon: Icons
                                                                        .camera_enhance_rounded,
                                                                    text: cubit
                                                                        .posts[index]
                                                                        .photographer),
                                                                rowAndIcon(
                                                                    icon: Icons
                                                                        .location_city,
                                                                    text: cubit
                                                                        .posts[index]
                                                                        .location),
                                                                rowAndIcon(
                                                                    icon: Icons
                                                                        .av_timer_sharp,
                                                                    text: cubit
                                                                        .posts[index]
                                                                        .date),
                                                                const SizedBox(
                                                                  height: 10,
                                                                ),
                                                                defaultButton(
                                                                  onPressed: () =>
                                                                      Navigator.pop(
                                                                          ctx),
                                                                  textButton: "أغلاق",
                                                                  backgroundColor:
                                                                      exitColor,
                                                                  width: 100,
                                                                  radius: 12,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    });
                                              },
                                              icon: Icons.info,
                                              color: Colors.black,
                                              iconSize: 30),
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: whiteColor,
                                            borderRadius: BorderRadius.circular(12),
                                            boxShadow: [shadow()],
                                          ),
                                          child: defaultIconButton(
                                              onPressed: () {
                                                share(
                                                    cubit.posts[index].photographer,
                                                    "",
                                                    "https://rain-app.com/shot/${cubit.posts[index].id}",
                                                    "chooserTitle");
                                                cubit.sendShare(
                                                    id: cubit.posts[index].id);
                                              },
                                              icon: Icons.share,
                                              color: Colors.black,
                                              iconSize: 30),
                                        )
                                      ],
                                    ))
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
            },
          );
        },
      ),
    );
  }
}

Widget rowAndIcon({required IconData icon, required String text}) {
  return Row(
    children: [
      Icon(
        icon,
        color: Colors.black,
      ),
      const SizedBox(
        width: 10,
      ),
      Expanded(
        child: Text(
          maxLines: 10,
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    ],
  );
}

// ignore: must_be_immutable
class PlayVideo extends StatefulWidget {
  late String url;
  // late int ratioHeight;
  // late int ratioWidth;
  PlayVideo(this.url, {super.key});
  @override
  State<PlayVideo> createState() => _PlayVideoState();
}

class _PlayVideoState extends State<PlayVideo> {
  late VideoPlayerController videoController;
  @override
  void initState() {
   // videoController = VideoPlayerController.network(widget.url)
    videoController = VideoPlayerController.networkUrl(Uri.parse(widget.url))
      ..initialize().then((value) {
        setState(() {});
      })
      ..setLooping(true)
      ..play();

    // chewieController = ChewieController(
    //   videoPlayerController: videoController,
    //   autoInitialize: true,

    //   autoPlay: true,
    //   // aspectRatio: widget.ratioWidth / widget.ratioHeight,
    //   allowFullScreen: true,
    //   looping: false,
    // );
    super.initState();
  }

  @override
  void dispose() {
    videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: videoController.value.isInitialized
          ? VideoPlayer(videoController)
          : Container(),
    );
  }
}

// ignore: must_be_immutable
class PlayV extends StatefulWidget {
  late String url;
  // late int ratioHeight;
  // late int ratioWidth;
  PlayV(this.url, {super.key});
  @override
  State<PlayV> createState() => _PlayVState();
}

class _PlayVState extends State<PlayV> {
  late VideoPlayerController videoController;
  @override
  void initState() {
   // videoController = VideoPlayerController.network(widget.url)
    videoController = VideoPlayerController.networkUrl(Uri.parse(widget.url))
      ..initialize().then((value) {
        setState(() {});
      })
      ..setLooping(true)
      ..play();

    // chewieController = ChewieController(
    //   videoPlayerController: videoController,
    //   autoInitialize: true,

    //   autoPlay: true,
    //   // aspectRatio: widget.ratioWidth / widget.ratioHeight,
    //   allowFullScreen: true,
    //   looping: false,
    // );
    super.initState();
  }

  @override
  void dispose() {
    videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: videoController.value.isInitialized
          ? VideoPlayer(videoController)
          : Container(),
    );
  }
}
