import 'package:google_mobile_ads/google_mobile_ads.dart';

class VideoModel {
  late final int id;
  late final String photographer;
  late final String location;
  late final String date;
  late final String schedule;
  late final String hide;
  late final int shares;
  late final String media;



  VideoModel.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? 0;
    photographer = json['photographer']?.toString() ?? "";
    location = json['location']?.toString() ?? "";
    date = json['date']?.toString() ?? "";
    schedule = json['schedule']?.toString() ?? "";
    hide = json['hide']?.toString() ?? "";
    shares = int.tryParse(json['shares'].toString()) ?? 0;
    media = json['media']?.toString() ?? "";
  }
}
