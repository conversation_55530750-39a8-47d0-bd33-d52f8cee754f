// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBKz0r2gh0wdUzV1mT1x_mY23s7VseHulQ',
    appId: '1:661086983322:web:fbd7fe5ac44ccfd7403b23',
    messagingSenderId: '661086983322',
    projectId: 'mater-6d130',
    authDomain: 'mater-6d130.firebaseapp.com',
    storageBucket: 'mater-6d130.appspot.com',
    measurementId: 'G-2WXV6YVDVJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAf7t6Uhn93xexL7uhnn6vVxnoYGpc0sDY',
    appId: '1:661086983322:android:5cf29ac4fed80c92403b23',
    messagingSenderId: '661086983322',
    projectId: 'mater-6d130',
    storageBucket: 'mater-6d130.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAUqIUArnfuxcqapgXcM1AncJgNe_Ud71c',
    appId: '1:661086983322:ios:49dea4342bb6f406403b23',
    messagingSenderId: '661086983322',
    projectId: 'mater-6d130',
    storageBucket: 'mater-6d130.appspot.com',
    androidClientId: '661086983322-dkc8souvp5ga7l4tsk59vb1r90on2t63.apps.googleusercontent.com',
    iosClientId: '661086983322-pf30foj35861dv24lhbm0mp3ekgvi7dl.apps.googleusercontent.com',
    iosBundleId: 'my.app.mattar',
  );
}
