PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Firebase/CoreOnly (10.18.0):
    - FirebaseCore (= 10.18.0)
  - firebase_core (2.24.2):
    - Firebase/CoreOnly (= 10.18.0)
    - Flutter
  - FirebaseCore (10.18.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.21.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - Flutter (1.0.0)
  - flutter_share (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - Google-Mobile-Ads-SDK (10.11.0):
    - GoogleAppMeasurement (< 11.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (1.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 10.11.0)
    - webview_flutter_wkwebview
  - GoogleAppMeasurement (10.21.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.21.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.21.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.21.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.21.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleUserMessagingPlatform (2.1.0)
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.3.1)
  - ReachabilitySwift (5.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_share (from `.symlinks/plugins/flutter_share/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - ReachabilitySwift
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_share:
    :path: ".symlinks/plugins/flutter_share/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  Firebase: 414ad272f8d02dfbf12662a9d43f4bba9bec2a06
  firebase_core: 0af4a2b24f62071f9bf283691c0ee41556dcb3f5
  FirebaseCore: 2322423314d92f946219c8791674d2f3345b598f
  FirebaseCoreInternal: 43c1788eaeee9d1b97caaa751af567ce11010d00
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_share: 4be0208963c60b537e6255ed2ce1faae61cd9ac2
  fluttertoast: 31b00dabfa7fb7bacd9e7dbee580d7a2ff4bf265
  Google-Mobile-Ads-SDK: 58b4fda3f9758fc1ed210aa5cf7777b5eb55d47e
  google_mobile_ads: 511febb4768edc860ee455a9e201ff52de385908
  GoogleAppMeasurement: a65314d316443969ed3d3709b3a187448ed6418f
  GoogleUserMessagingPlatform: dce302b8f1b84d6e945812ee7a15c3f65a102cbf
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  image_picker_ios: 99dfe1854b4fa34d0364e74a78448a0151025425
  in_app_purchase_storekit: 9e9931234f0adcf71ae323f8c83785b96030edf1
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  Toast: ec33c32b8688982cecc6348adeae667c1b9938da
  url_launcher_ios: bbd758c6e7f9fd7b5b1d4cde34d2b95fcce5e812
  video_player_avfoundation: e9e6f9cae7d7a6d9b43519b0aab382bca60fcfd1
  webview_flutter_wkwebview: 4f3e50f7273d31e5500066ed267e3ae4309c5ae4

PODFILE CHECKSUM: 224f7985c9c7f9be8d041beb5ba5de30e2edb59e

COCOAPODS: 1.15.2
