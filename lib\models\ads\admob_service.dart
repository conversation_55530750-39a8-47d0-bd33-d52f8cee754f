import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdmobServiceNew {

  static init(){
    MobileAds.instance.initialize();
  }


  ///---------------- Banner Ads ----------------------

  static final adUnitIdBanner = Platform.isAndroid
      ? 'ca-app-pub-2342851934476929/8822850492'
      : 'ca-app-pub-2342851934476929/9636969213';

  static final rewarderAds = Platform.isAndroid
     ?"ca-app-pub-2342851934476929/3015974131"
     :"-----------------------------------";

  static final getinitAd = Platform.isAndroid
      ? "-----------------------------------"
      :"-----------------------------------";



  static BannerAd? mapBannerAd;
  static bool isLoadedMap = false;

  static void showBannerAdH1(Function setState, context) async{
    await Future.delayed(Duration(milliseconds: 1));
    final size = MediaQuery.of(context).size;
    mapBannerAd = BannerAd(
      adUnitId: adUnitIdBanner,
      request: const AdRequest(),
      size: AdSize(width: size.width.toInt(), height: 70),
      listener: BannerAdListener(
        // Called when an ad is successfully received.
        onAdLoaded: (ad) {
           debugPrint('${ad.adUnitId} loaded from new .');
           setState(() {
             isLoadedMap = true;
           });
        },
        // Called when an ad request failed.
        onAdFailedToLoad: (ad, err) {
          debugPrint('BannerAd failed to load: $err');
          // Dispose the ad here to free resources.
          ad.dispose();
        },
      ),
    )..load();
  }


}

