<?php

namespace App\Http\Controllers;

use App\Models\Users;
use App\Models\OutlooksSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AuthController extends Controller
{
    /**
     * ✅ تسجيل الدخول مع تحويل صحيح للأنواع
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'type' => 'error',
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 400);
        }

        $user = Users::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'type' => 'error',
                'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
            ], 401);
        }

        // ✅ تحديث token المستخدم
        $user->token = Str::random(60);
        $user->save();

        // ✅ جلب الاشتراكات
        $subscriptions = OutlooksSubscription::where('user_id', $user->id)
            ->where('expires_at', '>', now())
            ->get();

        // ✅ تحويل البيانات للأنواع الصحيحة
        $formattedUser = [
            'id' => (int) $user->id,                    // ✅ تحويل إلى integer
            'name' => $user->name ?? '',
            'email' => $user->email ?? '',
            'password' => $user->password ?? '',
            'country' => $user->country ?? '',
            'phone' => $user->phone ?? '',
            'facebook_token' => $user->facebook_token ?? '',
            'google_token' => $user->google_token ?? '',
            'token' => $user->token,
            'role' => $user->role ?? '',
            'pic' => $user->pic ?? '',
            'date' => $user->date ?? '',
            'coupon' => $user->coupon ?? 'null',
            'ban' => (int) $user->ban,                  // ✅ تحويل إلى integer
            'subscription' => $subscriptions->map(function($sub) {
                return [
                    'id' => (int) $sub->id,             // ✅ تحويل إلى integer
                    'user_id' => (int) $sub->user_id,   // ✅ تحويل إلى integer
                    'amount' => $sub->amount ?? '',
                    'start_date' => $sub->start_date ?? '',
                    'expire_date' => $sub->expires_at ?? '',
                    'marketer_name' => $sub->marketer_name ?? '',
                    'pay_method' => $sub->pay_method ?? '',
                    'active' => (int) $sub->active      // ✅ تحويل إلى integer
                ];
            })->toArray()
        ];

        return response()->json($formattedUser);
    }

    /**
     * ✅ إنشاء حساب جديد مع تحويل صحيح للأنواع
     */
    public function signup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'country' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'type' => 'error',
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 400);
        }

        $user = Users::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'country' => $request->country,
            'token' => Str::random(60),
            'date' => now()->format('Y-m-d'),
            'ban' => 0,
            'role' => 'user'
        ]);

        // ✅ تحويل البيانات للأنواع الصحيحة
        $formattedUser = [
            'id' => (int) $user->id,                    // ✅ تحويل إلى integer
            'name' => $user->name,
            'email' => $user->email,
            'country' => $user->country,
            'token' => $user->token,
            'role' => $user->role,
            'pic' => $user->pic ?? '',
            'date' => $user->date,
            'coupon' => $user->coupon ?? 'null',
            'ban' => (int) $user->ban,                  // ✅ تحويل إلى integer
            'phone' => $user->phone ?? '',
            'facebook_token' => $user->facebook_token ?? '',
            'google_token' => $user->google_token ?? '',
            'subscription' => []
        ];

        return response()->json([
            'type' => 'success',
            'message' => 'تم إنشاء الحساب بنجاح',
            'user' => $formattedUser
        ], 201);
    }

    /**
     * ✅ إرسال رابط إعادة تعيين كلمة المرور
     */
    public function sendResetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'type' => 'error',
                'message' => 'البريد الإلكتروني غير موجود'
            ], 400);
        }

        $user = Users::where('email', $request->email)->first();
        $resetCode = rand(100000, 999999);

        // حفظ الكود في قاعدة البيانات أو cache
        $user->reset_code = $resetCode;
        $user->reset_code_expires_at = now()->addMinutes(30);
        $user->save();

        // إرسال الكود عبر البريد الإلكتروني
        try {
            Mail::send('emails.reset-password', ['code' => $resetCode], function($message) use ($user) {
                $message->to($user->email);
                $message->subject('كود إعادة تعيين كلمة المرور');
            });

            return response()->json([
                'type' => 'success',
                'message' => 'تم إرسال كود إعادة التعيين إلى بريدك الإلكتروني'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => 'فشل في إرسال البريد الإلكتروني'
            ], 500);
        }
    }

    /**
     * ✅ التحقق من كود إعادة التعيين
     */
    public function checkResetCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'type' => 'error',
                'message' => 'بيانات غير صحيحة'
            ], 400);
        }

        $user = Users::where('email', $request->email)
            ->where('reset_code', $request->code)
            ->where('reset_code_expires_at', '>', now())
            ->first();

        if (!$user) {
            return response()->json([
                'type' => 'error',
                'message' => 'الكود غير صحيح أو منتهي الصلاحية'
            ], 400);
        }

        return response()->json([
            'type' => 'success',
            'message' => 'الكود صحيح'
        ]);
    }

    /**
     * ✅ إعادة تعيين كلمة المرور
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code' => 'required|string',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'type' => 'error',
                'message' => 'بيانات غير صحيحة'
            ], 400);
        }

        $user = Users::where('email', $request->email)
            ->where('reset_code', $request->code)
            ->where('reset_code_expires_at', '>', now())
            ->first();

        if (!$user) {
            return response()->json([
                'type' => 'error',
                'message' => 'الكود غير صحيح أو منتهي الصلاحية'
            ], 400);
        }

        // تحديث كلمة المرور
        $user->password = Hash::make($request->password);
        $user->reset_code = null;
        $user->reset_code_expires_at = null;
        $user->save();

        return response()->json([
            'type' => 'success',
            'message' => 'تم تغيير كلمة المرور بنجاح'
        ]);
    }

    /**
     * ✅ جلب بيانات المستخدم (Profile)
     */
    public function profile(Request $request)
    {
        $token = $request->header('Authorization');
        
        if (!$token) {
            return response()->json([
                'type' => 'error',
                'message' => 'Token مطلوب'
            ], 401);
        }

        $user = Users::where('token', $token)->first();

        if (!$user) {
            return response()->json([
                'type' => 'error',
                'message' => 'مستخدم غير موجود'
            ], 404);
        }

        // التحقق من الاشتراكات النشطة
        $activeSubscription = OutlooksSubscription::where('user_id', $user->id)
            ->where('expires_at', '>', now())
            ->exists();

        // ✅ تحويل البيانات للأنواع الصحيحة
        $formattedUser = [
            'id' => (int) $user->id,                    // ✅ تحويل إلى integer
            'name' => $user->name ?? '',
            'email' => $user->email ?? '',
            'country' => $user->country ?? '',
            'phone' => $user->phone ?? '',
            'facebook_token' => $user->facebook_token ?? '',
            'google_token' => $user->google_token ?? '',
            'token' => $user->token,
            'role' => $user->role ?? '',
            'pic' => $user->pic ?? '',
            'date' => $user->date ?? '',
            'coupon' => $user->coupon ?? 'null',
            'ban' => (int) $user->ban,                  // ✅ تحويل إلى integer
            'sub' => $activeSubscription                // حالة الاشتراك
        ];

        return response()->json($formattedUser);
    }
}
