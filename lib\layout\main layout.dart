import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mattar/component/component.dart';
import 'package:mattar/component/constants.dart';
import 'package:mattar/cubit/cubit/app_cubit.dart';
import 'package:mattar/network/local/shared_pref.dart';
import 'package:url_launcher/url_launcher.dart';

class MainLayout extends StatelessWidget {
  const MainLayout({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppCubit()
        ..checkCopon()
        ..getProfileData(),
      child: BlocConsumer<AppCubit, ApppState>(
        listener: (context, state) {},
        builder: (context, state) {
          var cubit = AppCubit.caller(context);
          List title = [
            "التوقعات ومتابعة الحالات",
            "خرائط الطقس",
            "صور ومقاطع الطقس"
          ];

          return Scaffold(
            drawer: InkWell(
              onTap: () {},
              child: Drawer(
                backgroundColor: backgroundColor,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 60,
                    ),
                    Container(
                      height: 100,
                      width: 100,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100)),
                      child:
                      AppCubit.caller(context).profile?.pic != null &&
                              AppCubit.caller(context).profile?.pic != ""
                          ? CachedNetworkImage(
                              imageUrl:
                                  "https://admin.rain-app.com/storage/users/${cubit.profile?.pic}",
                              imageBuilder: (context, imageProvider) =>
                                  Container(
                                    width: 100,
                                    height: 100,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                      image: DecorationImage(
                                        image: imageProvider,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                              placeholder: (context, url) => const Center(
                                      child: CircularProgressIndicator(
                                    color: Color.fromRGBO(66, 105, 129, 1),
                                  )),
                              errorWidget: (_, __, ___) =>
                                  Image.asset("images/logo.png"))
                          : const CircleAvatar(
                              radius: 100,
                              backgroundImage: AssetImage("images/logo.png")),
                    ),
                    const SizedBox(
                      height: 2,
                    ),
                    CacheHelper.getData(key: "token") != null &&
                            cubit.profile?.name != null
                        ? Text("مرحبا ${cubit.profile?.name}")
                        : const SizedBox(
                            height: 1,
                          ),
                    const SizedBox(
                      height: 8,
                    ),
                    Expanded(
                      child: ListView(
                        children: [
                          if (CacheHelper.getData(
                                    key: "login",
                                  ) ==
                                  false ||
                              CacheHelper.getData(
                                    key: "login",
                                  ) ==
                                  null)
                            defaultDrawerContainer(
                                context: context,
                                title: "تسجيل الدخول",
                                onpressed: () {
                                  Navigator.of(context)
                                      .pushReplacementNamed("login");
                                })
                          else
                            defaultDrawerContainer(
                                context: context,
                                title: "زيارة الملف الشخصي",
                                onpressed: () {
                                  Navigator.of(context)
                                      .pushReplacementNamed("user acount");
                                }),
                          // defaultDrawerContainer(
                          //     context: context,
                          //     title: "إشتراك",
                          //     onpressed: () =>
                          //         Navigator.of(context).pushNamed("ads")),
                          defaultDrawerContainer(
                              context: context,
                              title: "الكوبونات",
                              onpressed: () {
                                Navigator.of(context)
                                    .pushReplacementNamed("myCopons");
                              }),
                          defaultDrawerContainer(
                              context: context,
                              title: "عن التطبيق",
                              onpressed: () {
                                Navigator.of(context)
                                    .pushReplacementNamed("about");
                              }),
                          defaultDrawerContainer(
                              context: context,
                              title: "بلغ عن مشكلة",
                              onpressed: () => Navigator.of(context)
                                  .pushReplacementNamed("problem")),
              ((defaultTargetPlatform == TargetPlatform.android)?
                          defaultDrawerContainer(
                              context: context,
                              title: "تقييم التطبيق",
                              onpressed: () async {
                                launchUrl(Uri.parse('https://play.google.com/store/apps/details?id=my.app.mattar'),mode: LaunchMode.inAppWebView);

                              }): (defaultTargetPlatform == TargetPlatform.iOS)?
          defaultDrawerContainer(
              context: context,
              title: "تقييم التطبيق",
              onpressed: () async {
                launchUrl(Uri.parse(
                    'https://apps.apple.com/app/%D9%85%D8%B7%D8%B1/id6446261646'),
                    mode: LaunchMode.inAppWebView);
              }):SizedBox()),
                          ((defaultTargetPlatform == TargetPlatform.android)?
                          defaultDrawerContainer(
                              context: context,
                              title: "مشاركة التطبيق",
                              onpressed: () {
                                Share.share(
                                    "مطر\nحمل تطبيق مطر وتابع عبره اخر مستجدات الطقس\nhttps://play.google.com/store/apps/details?id=my.app.mattar",
                                    subject: "مطر"
                                );
                              }):(defaultTargetPlatform == TargetPlatform.iOS)?
                          defaultDrawerContainer(
                              context: context,
                              title: "مشاركة التطبيق",
                              onpressed: () {
                                Share.share(
                                  "مطر\nحمل تطبيق مطر وتابع عبره اخر مستجدات الطقس\nhttps://apps.apple.com/app/%D9%85%D8%B7%D8%B1/id6446261646",
                                  subject: "مطر"
                                );
                              }):SizedBox()),
                          defaultDrawerContainer(
                              context: context,
                              title: "تطبيقات ومواقع مفيدة",
                              onpressed: () => Navigator.of(context)
                                  .pushReplacementNamed("apps")),
                          defaultDrawerContainer(
                              context: context,
                              title: "تابعنا عبر وسائل التواصل الاجتماعي ",
                              onpressed: () => Navigator.of(context)
                                  .pushReplacementNamed("social")),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            appBar: AppBar(
              title: Text(
                title[cubit.currentIndex],
                style: const TextStyle(fontSize: 16),
              ),
              actions: [
                CacheHelper.getData(key: "index") == 1
                    ? const SizedBox()
                    : IconButton(
                        onPressed: () {
                          Navigator.of(context)
                              .pushReplacementNamed("main layout");
                        },
                        icon: const Icon(Icons.refresh_rounded)),
                IconButton(
                    onPressed: () {
                      Navigator.of(context).pushNamed("notification");
                    },
                    icon: const Icon(Icons.notifications_active)),
              ],
              backgroundColor: const Color.fromRGBO(66, 105, 129, 1),
            ),
            body: cubit.bottomNavScreen[cubit.currentIndex],
            bottomNavigationBar: BottomNavigationBar(
                backgroundColor: whiteColor,
                selectedItemColor: Colors.black,
                unselectedItemColor: Colors.grey,
                elevation: 0,
                type: BottomNavigationBarType.fixed,
                currentIndex: cubit.currentIndex,
                unselectedLabelStyle: const TextStyle(color: Colors.grey),
                onTap: (index) {
                  cubit.changeBottomNavIndex(index);
                },
                items: [
                  BottomNavigationBarItem(
                      icon: InkWell(
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(50),
                              color: cubit.currentIndex == 0
                                  ? const Color.fromRGBO(66, 105, 129, 1)
                                  : null),
                          child: Image.asset(
                            "images/weather.png",
                            height: 25,
                          ),
                        ),
                      ),
                      label: "التوقعات ومتابعة الحالات"),
                  BottomNavigationBarItem(
                      icon: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(50),
                            color: cubit.currentIndex == 1
                                ? const Color.fromRGBO(66, 105, 129, 1)
                                : null),
                        child: Image.asset(
                          "images/satellite.png",
                          height: 25,
                        ),
                      ),
                      label: title[1]),
                  BottomNavigationBarItem(
                      label: title[2],
                      icon: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(50),
                            color: cubit.currentIndex == 2
                                ? const Color.fromRGBO(66, 105, 129, 1)
                                : null),
                        child: Image.asset(
                          "images/camera.png",
                          height: 25,
                        ),
                      )),
                ]),
          );
        },
      ),
    );
  }
}
