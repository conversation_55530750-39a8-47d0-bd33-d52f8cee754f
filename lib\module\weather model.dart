import 'package:google_mobile_ads/google_mobile_ads.dart';

class WeatherModel {
  WeatherModel({
    required this.id,
    required this.title,
    required this.date,
    required this.country,
    required this.details,
    required this.schedule,
    required this.hide,
    required this.likes,
    required this.shares,
    required this.files,
    required this.comments,
    required this.addLoaded,
    required this.addLoaded2,
    required this.bannerAdH1,
    required this.bannerAdH2,
  });
  late final int id;
  late final String title;
  late final String date;
  late final String country;
  late final String details;
  late final String schedule;
  late final String hide;
  late final int likes;
  late final int shares;
  late final List<Files> files;
  late final List<Comments> comments;
  bool? liked = false;
  bool? addLoaded = false;
  bool? addLoaded2 = false;
  BannerAd? bannerAdH1 = null;
  BannerAd? bannerAdH2 = null;

  WeatherModel.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? 0;
    title = json['title']?.toString() ?? "";
    date = json['date']?.toString() ?? "";
    country = json['country']?.toString() ?? "";
    details = json['details']?.toString() ?? "";
    schedule = json['schedule']?.toString() ?? "";
    hide = json['hide']?.toString() ?? "";
    likes = int.tryParse(json['likes'].toString()) ?? 0;
    shares = int.tryParse(json['shares'].toString()) ?? 0;
    files = List.from(json['files']).map((e) => Files.fromJson(e)).toList();
    comments =
        List.from(json['comments']).map((e) => Comments.fromJson(e)).toList();
  }
}

class Files {
  Files({
    required this.id,
    required this.outlookId,
    required this.file,
  });
  late final int id;
  late final String outlookId;
  late final String file;
  Files.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? 0;
    outlookId = json['outlook_id']?.toString() ?? "";
    file = json['file']?.toString() ?? "";
  }
}

class Comments {
  Comments({
    required this.id,
    required this.outlookId,
    required this.userId,
    required this.comment,
    required this.reply,
    required this.date,
    required this.user,
  });
  late final int id;
  late final int outlookId;
  late final int userId;
  late final String comment;
  late final String reply;
  late final String date;
  late final UserC user;

  Comments.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? 0;
    outlookId = int.tryParse(json['outlook_id'].toString()) ?? 0;
    userId = int.tryParse(json['user_id'].toString()) ?? 0;
    comment = json['comment']?.toString() ?? "";
    reply = json['reply']?.toString() ?? "";
    date = json['date']?.toString() ?? "";
    user = UserC.fromJson(json['user'] ??
        {
          "id": 532698,
          "name": "غير معروف",
          "email": "<EMAIL>",
          "country": "",
          "phone": "",
          "facebookToken": "",
          "googleToken": "",
          "token": "gfhghjghjgjgjghj",
          "role": "",
          "pic": "",
          "date": "",
          "coupon": "",
          "ban": 0
        });
  }
}

class UserC {
  UserC({
    required this.id,
    required this.name,
    required this.email,
    required this.country,
    required this.phone,
    required this.facebookToken,
    required this.googleToken,
    required this.token,
    required this.role,
    required this.pic,
    required this.date,
    required this.coupon,
    required this.ban,
  });
  late final int id;
  late final String name;
  late final String email;
  late final String country;
  late final String phone;
  late final String facebookToken;
  late final String googleToken;
  late final String token;
  late final String role;
  late final String pic;
  late final String date;
  late final String coupon;
  late final int ban;

  UserC.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? 0;
    name = json['name']?.toString() ?? "";
    email = json['email']?.toString() ?? "";
    country = json['country']?.toString() ?? "";
    phone = json['phone']?.toString() ?? "";
    facebookToken = json['facebook_token']?.toString() ?? "";
    googleToken = json['google_token']?.toString() ?? "";
    token = json['token']?.toString() ?? "";
    role = json['role']?.toString() ?? "user";
    pic = json["pic"]?.toString() ?? "";
    date = json['date']?.toString() ?? "";
    coupon = json["coupon"]?.toString() ?? "";
    ban = int.tryParse(json['ban'].toString()) ?? 0;
  }
}

        //  cubit.profile?.role == "admin"
        //                                             ? Expanded(
        //                                                 child: Container(
        //                                                   margin:
        //                                                       EdgeInsets.only(
        //                                                           bottom: 8),
        //                                                   child:
        //                                                       ListView.builder(
        //                                                           itemCount: cubit
        //                                                               .post[
        //                                                                   index]
        //                                                               .comments
        //                                                               .length,
        //                                                           itemBuilder:
        //                                                               (__,
        //                                                                   ind) {
        //                                                             return Column(
        //                                                               crossAxisAlignment:
        //                                                                   CrossAxisAlignment
        //                                                                       .start,
        //                                                               children: [
        //                                                                 Row(
        //                                                                   children: [
        //                                                                     Container(
        //                                                                       height: 40,
        //                                                                       width: 40,
        //                                                                       decoration: BoxDecoration(borderRadius: BorderRadius.circular(200)),
        //                                                                       child: ClipRRect(
        //                                                                         borderRadius: BorderRadius.circular(200),
        //                                                                         child: AppCubit.caller(context).profile?.pic != null && AppCubit.caller(context).profile?.pic != ""
        //                                                                             ? Image.network(errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
        //                                                                                 return const Text('Your error widget...');
        //                                                                               }, "https://admin.rain-app.com/storage/users/${AppCubit.caller(context).profile?.pic}")
        //                                                                             : Image.asset("images/avatar.png"),
        //                                                                       ),
        //                                                                     ),
        //                                                                     Container(
        //                                                                       margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
        //                                                                       padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
        //                                                                       child: Column(
        //                                                                         crossAxisAlignment: CrossAxisAlignment.start,
        //                                                                         children: [
        //                                                                           Text(
        //                                                                             cubit.post[index].comments[ind].user.name,
        //                                                                             style: const TextStyle(color: const Color.fromRGBO(66, 105, 129, 1), fontWeight: FontWeight.bold),
        //                                                                           ),
        //                                                                           Flexible(
        //                                                                             fit: FlexFit.tight,
        //                                                                             child: Container(
        //                                                                               child: SingleChildScrollView(
        //                                                                                 child: Text(
        //                                                                                   cubit.post[index].comments[ind].comment,
        //                                                                                   style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold, color: Colors.grey),
        //                                                                                 ),
        //                                                                               ),
        //                                                                             ),
        //                                                                           ),
        //                                                                           Row(
        //                                                                             children: [
        //                                                                               Text(
        //                                                                                 "${DateFormat.yMd().format(DateTime.parse(cubit.post[index].comments[ind].date))}",
        //                                                                                 style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.grey),
        //                                                                               ),
        //                                                                               SizedBox(
        //                                                                                 width: 10,
        //                                                                               ),
        //                                                                               InkWell(
        //                                                                                 onTap: () {
        //                                                                                   showDialog(
        //                                                                                       context: context,
        //                                                                                       builder: (BuildContext context) {
        //                                                                                         TextEditingController replayController = TextEditingController();
        //                                                                                         return AlertDialog(
        //                                                                                           actions: [
        //                                                                                             Form(
        //                                                                                               child: Row(
        //                                                                                                 mainAxisAlignment: MainAxisAlignment.center,
        //                                                                                                 children: [
        //                                                                                                   Container(
        //                                                                                                     color: Colors.white,
        //                                                                                                     width: MediaQuery.of(context).size.width * 0.7,
        //                                                                                                     margin: const EdgeInsets.only(top: 1, right: 10),
        //                                                                                                     child: defaultFormField(prefixIcon: const Icon(Icons.comment), controller: replayController, type: TextInputType.text),
        //                                                                                                   ),
        //                                                                                                   defaultIconButton(
        //                                                                                                       onPressed: () {
        //                                                                                                         cubit.sendReplay(outlookId: cubit.posts[index].id, commentId: cubit.posts[index].comments[ind].id.toInt(), reply: replayController.text);
        //                                                                                                         Navigator.pop(context);
        //                                                                                                       },
        //                                                                                                       icon: Icons.send,
        //                                                                                                       color: mainColor)
        //                                                                                                 ],
        //                                                                                               ),
        //                                                                                             )
        //                                                                                           ],
        //                                                                                         );
        //                                                                                       });
        //                                                                                 },
        //                                                                                 child: Text(
        //                                                                                   "رد علي التعليق",
        //                                                                                   style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.black),
        //                                                                                 ),
        //                                                                               ),
        //                                                                             ],
        //                                                                           )
        //                                                                         ],
        //                                                                       ),
        //                                                                     ),
        //                                                                   ],
        //                                                                 ),
        //                                                                 cubit.posts[index].comments[ind].reply.isEmpty
        //                                                                     ? const SizedBox()
        //                                                                     : Row(
        //                                                                         children: [
        //                                                                           Container(
        //                                                                             height: 35,
        //                                                                             width: 35,
        //                                                                             child: ClipRRect(
        //                                                                               borderRadius: BorderRadius.circular(200),
        //                                                                               child: Image.asset("images/icon.png"),
        //                                                                             ),
        //                                                                           ),
        //                                                                           Flexible(
        //                                                                             fit: FlexFit.tight,
        //                                                                             child: Container(
        //                                                                               padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        //                                                                               child: SingleChildScrollView(
        //                                                                                 child: Text(
        //                                                                                   cubit.post[index].comments[ind].reply,
        //                                                                                   style: const TextStyle(fontSize: 18, color: Colors.grey),
        //                                                                                 ),
        //                                                                               ),
        //                                                                             ),
        //                                                                           ),
        //                                                                         ],
        //                                                                       ),
        //                                                               ],
        //                                                             );
        //                                                           }),
        //                                                 ),
        //                                               )
        //                                             : const Text(
        //                                                 "tttttttttttttt"),