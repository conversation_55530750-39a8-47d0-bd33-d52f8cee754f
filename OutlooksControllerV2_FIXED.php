<?php

namespace App\Http\Controllers;

use App\Models\Comments;
use App\Models\Countries;
use App\Models\Outlook;
use App\Models\OutlookLikes;
use App\Models\OutlooksFiles;
use App\Models\OutlookShares;
use App\Models\OutlooksSubscription;
use App\Models\SystemSettings;
use App\Models\Users;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;

class OutlooksControllerV2 extends Controller
{
    public function get(Request $request)
    {
        if(request()->get("onlyFutureScheduled", false))
        {
            $now = now();

            $outlooks = Outlook::query()
                ->where("schedule", ">", $now)
                ->orderBy('id', "DESC")
                ->with("files", 'comments')
                ->paginate(10);
        } else {
            $now = request()->get("ignoreSchedule", false) ? now()->addCenturiesNoOverflow(50) : now();

            $outlooks = Outlook::query()
                ->where(function ($query) use ($now) {
                    $query->where("schedule", "<=", $now)
                        ->orWhereNull("schedule");
                })
                ->orderBy('id', 'DESC')
                ->with('files', 'comments')
                ->paginate(10);
        }

        // ✅ تحويل البيانات للأنواع الصحيحة
        return $this->getOutlooksArrayFixed($outlooks, $request);
    }

    public function getByCountry($country, Request $request)
    {
        $now = request()->get("ignoreSchedule", false) ? now()->addCenturiesNoOverflow(50) : now();
        $getCountry = Countries::where('id', $country)->first();

        // /api/outlooks/عام
        if($country == "public"){
            $outlooks = Outlook::query()->where(function ($query) use ($now) {
                $query->where("schedule", "<=", $now)->orWhereNull("schedule");
            })->where('country', "عام")
                ->with('files', 'comments')
                ->orderBy('id', 'DESC')
                ->paginate(10);

            // ✅ تحويل البيانات للأنواع الصحيحة
            return $this->getOutlooksArrayFixed($outlooks, $request);
        }

        if ($getCountry !== null) {
            // check if user has subscription
            $subscription_mode = SystemSettings::select(['subscription_mode', 'subscription_length'])->where("id", 1)->first()['subscription_mode'];
            if($subscription_mode){

                if(!$request->hasHeader('Authorization') ) {return response()->json(['type' => 'error', 'message' => "Authorization header is required"]);}

                $user_token = $request->header('Authorization');

                $latest_subscription = OutlooksSubscription::where("user_id", $user_token)->where('country', $country)->latest()->first();

                if(is_null($latest_subscription)) return response()->json([
                        'type' => 'error',
                        'message' => 'عذراً، لا يوجد لديك اشتراك لمشاهدة هذه الصفحة.']
                );

                if(now() > $latest_subscription->expires_at)
                {
                    $since = Carbon::make(($latest_subscription->expires_at))->diffForHumans();

                    return response()->json([
                        'type' => 'error',
                        'message' =>  "عذراً، انتهي هذا الاشتراك منذ {$since} ",
                    ]);
                }
            }

            $outlooks = Outlook::query()
                ->where(function ($query) use ($now) {
                    $query->where("schedule", "<=", $now)
                        ->orWhereNull("schedule");
                })
                ->where('country', $getCountry->country)
                ->with('files', 'comments')
                ->orderBy('id', 'DESC')
                ->paginate(10);

            // ✅ تحويل البيانات للأنواع الصحيحة
            return $this->getOutlooksArrayFixed($outlooks, $request);

        } else {
            return response()->json(['alert' => 'لا توجد بيانات'], 404);
        }
    }

    public function fetch($id)
    {
        $outlook = Outlook::where('id', $id)->with('files', 'comments')->first();
        
        if (!$outlook) {
            return response()->json(['alert' => 'المنشور غير موجود'], 404);
        }

        // ✅ تحويل البيانات للأنواع الصحيحة
        $formattedOutlook = [
            'id' => (int) $outlook->id,
            'title' => $outlook->title,
            'date' => $outlook->date,
            'country' => $outlook->country,
            'details' => $outlook->details,
            'schedule' => $outlook->schedule,
            'hide' => $outlook->hide,
            'likes' => (int) $outlook->likes,        // ✅ تحويل إلى integer
            'shares' => (int) $outlook->shares,      // ✅ تحويل إلى integer
            'files' => $outlook->files->map(function($file) {
                return [
                    'id' => (int) $file->id,
                    'outlook_id' => (int) $file->outlook_id,  // ✅ تحويل إلى integer
                    'file' => $file->file
                ];
            }),
            'comments' => $outlook->comments->map(function($comment) {
                return [
                    'id' => (int) $comment->id,
                    'outlook_id' => (int) $comment->outlook_id,
                    'user_id' => (int) $comment->user_id,
                    'comment' => $comment->comment,
                    'reply' => $comment->reply,
                    'date' => $comment->date,
                    'user' => $comment->user ? [
                        'id' => (int) $comment->user->id,
                        'name' => $comment->user->name,
                        'email' => $comment->user->email,
                        'country' => $comment->user->country,
                        'phone' => $comment->user->phone,
                        'facebook_token' => $comment->user->facebook_token,
                        'google_token' => $comment->user->google_token,
                        'token' => $comment->user->token,
                        'role' => $comment->user->role,
                        'pic' => $comment->user->pic,
                        'date' => $comment->user->date,
                        'coupon' => $comment->user->coupon,
                        'ban' => (int) $comment->user->ban
                    ] : null
                ];
            })
        ];

        return response()->json($formattedOutlook);
    }

    public function fetchComments()
    {
        $comments = Comments::orderBy('id', 'DESC')->with(['user', 'outlook'])->get();
        
        // ✅ تحويل البيانات للأنواع الصحيحة
        $formattedComments = $comments->map(function($comment) {
            return [
                'id' => (int) $comment->id,
                'outlook_id' => (int) $comment->outlook_id,
                'user_id' => (int) $comment->user_id,
                'comment' => $comment->comment,
                'reply' => $comment->reply,
                'date' => $comment->date,
                'user' => $comment->user ? [
                    'id' => (int) $comment->user->id,
                    'name' => $comment->user->name,
                    'email' => $comment->user->email,
                    'country' => $comment->user->country,
                    'phone' => $comment->user->phone,
                    'facebook_token' => $comment->user->facebook_token,
                    'google_token' => $comment->user->google_token,
                    'token' => $comment->user->token,
                    'role' => $comment->user->role,
                    'pic' => $comment->user->pic,
                    'date' => $comment->user->date,
                    'coupon' => $comment->user->coupon,
                    'ban' => (int) $comment->user->ban
                ] : null,
                'outlook' => $comment->outlook ? [
                    'id' => (int) $comment->outlook->id,
                    'title' => $comment->outlook->title,
                    'date' => $comment->outlook->date,
                    'country' => $comment->outlook->country,
                    'details' => $comment->outlook->details,
                    'schedule' => $comment->outlook->schedule,
                    'hide' => $comment->outlook->hide,
                    'likes' => (int) $comment->outlook->likes,
                    'shares' => (int) $comment->outlook->shares
                ] : null
            ];
        });

        return response()->json($formattedComments);
    }

    /**
     * ✅ دالة جديدة مُصححة لتحويل البيانات للأنواع الصحيحة
     */
    private function getOutlooksArrayFixed(\Illuminate\Contracts\Pagination\LengthAwarePaginator $outlooks, Request $request): array
    {
        $items = collect($outlooks->items())->map(function ($outlook) use ($request) {
            $user = Users::where('token', $request->header('Authorization'))->first();
            $liked = false;

            if (!is_null($user)) {
                $liked = OutlookLikes::where('outlook_id', $outlook->id)->where('user_id', $user->id)->first();
            }

            return [
                'id' => (int) $outlook->id,
                'title' => $outlook->title,
                'date' => $outlook->date,
                'country' => $outlook->country,
                'details' => $outlook->details,
                'schedule' => $outlook->schedule,
                'hide' => $outlook->hide,
                'likes' => (int) $outlook->likes,        // ✅ تحويل إلى integer
                'shares' => (int) $outlook->shares,      // ✅ تحويل إلى integer
                'liked' => $liked ? true : false,
                'files' => $outlook->files->map(function($file) {
                    return [
                        'id' => (int) $file->id,
                        'outlook_id' => (int) $file->outlook_id,  // ✅ تحويل إلى integer
                        'file' => $file->file
                    ];
                }),
                'comments' => $outlook->comments->map(function($comment) {
                    return [
                        'id' => (int) $comment->id,
                        'outlook_id' => (int) $comment->outlook_id,
                        'user_id' => (int) $comment->user_id,
                        'comment' => $comment->comment,
                        'reply' => $comment->reply,
                        'date' => $comment->date,
                        'user' => $comment->user ? [
                            'id' => (int) $comment->user->id,
                            'name' => $comment->user->name,
                            'email' => $comment->user->email,
                            'country' => $comment->user->country,
                            'phone' => $comment->user->phone,
                            'facebook_token' => $comment->user->facebook_token,
                            'google_token' => $comment->user->google_token,
                            'token' => $comment->user->token,
                            'role' => $comment->user->role,
                            'pic' => $comment->user->pic,
                            'date' => $comment->user->date,
                            'coupon' => $comment->user->coupon,
                            'ban' => (int) $comment->user->ban
                        ] : null
                    ];
                })
            ];
        });

        $outlooks = $outlooks->toArray();
        unset($outlooks['data']);
        $outlooks['data'] = $items->toArray();
        return $outlooks;
    }

    public function add(Request $request)
    {
        $validate = $request->validate([
            'files.*' => 'required|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);

        if ($validate) {
            $messaging = app('firebase.messaging');
            $dataDecode = json_decode($request->data, true);
            $shouldCreatePublicRecord = (bool)$dataDecode['is_public'];

            if(is_array($dataDecode['country']) && count($dataDecode['country']) > 0) {
                foreach($dataDecode['country'] as $country) {
                    // process each country
                    $getOutlook = Outlook::create([
                        'title' => $dataDecode['title'],
                        'date' => $dataDecode['date'],
                        'country' => $country,
                        'details' => $dataDecode['details'],
                        'schedule' => $dataDecode['schedule'],
                        'hide' => $dataDecode['hideDate'],
                        'likes' => 0,
                        'shares' => 0,
                    ]);

                    $country_id = Countries::select('id')->where("country", $country)->first()->id;
                    $messaging->send(
                        \Kreait\Firebase\Messaging\CloudMessage::withTarget('topic', $country_id)
                            ->withNotification(\Kreait\Firebase\Messaging\Notification::create('توقع جديد', 'هناك توقع جديد'))
                            ->withData($getOutlook->toArray())
                    );

                    foreach ($request->file('files') as $file) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file_path = $file->storeAs('/outlooks/', $filename, 'public');
                        OutlooksFiles::updateOrCreate([
                            'outlook_id' => $getOutlook->id,
                            'file' => $filename
                        ]);
                    }
                }

                if($shouldCreatePublicRecord){
                    // store the outlook as general outlook if no countries are provided
                    // /api/outlooks/public
                    $this->createPublicOutlookRecord($dataDecode, $request);
                    return true;
                }

                return true;

            } else{
                if($shouldCreatePublicRecord){
                    // store the outlook as general outlook if no countries are provided
                    // /api/outlooks/public
                    $this->createPublicOutlookRecord($dataDecode, $request);

                    return true;
                }

                return abort(500, "Countries needs to be type of an array");
            }
        }

        return abort(500);
    }

    public function edit(Request $request)
    {
        $validate = $request->validate([
            'files.*' => 'nullable|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);

        $dataDecode = json_decode($request->data, true);

        $outlook = Outlook::where("id", $dataDecode['id'])->first();

        if(is_null($outlook)) return abort(404);

        Outlook::where('id', $dataDecode['id'])->update([
            'title' => $dataDecode['title'],
            'date' => $dataDecode['date'],
            'country' => $dataDecode['country'],
            'details' => $dataDecode['details'],
            'schedule' => $dataDecode['schedule'],
            'hide' => $dataDecode['hideDate'],
        ]);

        if($request->hasFile('files'))
        {
            // delete old files
            foreach($outlook->files as $file)
            {
                // delete the stored file
                if($file->DeleteStorageFile())
                    // delete the database record
                    OutlooksFiles::where('id', $file->id)->delete();
            }

            // upload new files
            foreach ($request->file('files') as $file) {
                $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                $file_path = $file->storeAs('/outlooks/', $filename, 'public');
                OutlooksFiles::updateOrCreate([
                    'outlook_id' => $outlook->id,
                    'file' => $filename
                ]);
            }
        }

        return true;
    }

    public function delete(Request $request)
    {
        Outlook::where('id', $request->id)->delete();
        Comments::where('outlook_id', $request->id)->delete();
        OutlooksFiles::where('outlook_id', $request->id)->delete();
        OutlookLikes::where('outlook_id', $request->id)->delete();
        OutlookShares::where('outlook_id', $request->id)->delete();
    }

    public function multiDelete(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Outlook::where('id', $id)->delete();
            Comments::where('outlook_id', $id)->delete();
            OutlooksFiles::where('outlook_id', $id)->delete();
            OutlookLikes::where('outlook_id', $id)->delete();
            OutlookShares::where('outlook_id', $id)->delete();
        }

        return true;
    }

    public function reply(Request $request)
    {
        Comments::where('id', $request->comment_id)->update([
            'reply' => $request->reply
        ]);
    }

    public function sendComment(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        Comments::create([
            'outlook_id' => $request->outlook_id,
            'user_id' => $user->id,
            'comment' => $request->comment,
            'date' => date('Y-m-d')
        ]);
    }

    public function sendReply(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        Comments::where([
            ['id', $request->comment_id],
            ['outlook_id', $request->outlook_id]
        ])->update([
            'reply' => $request->reply
        ]);
    }

    public function like(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        $getOutlook = Outlook::where('id', $request->outlook_id)->first();
        if ($getOutlook !== null) {
            $checkLike = OutlookLikes::where('outlook_id', $request->outlook_id)->where('user_id', $user->id)->first();
            if ($checkLike == null) {
                OutlookLikes::updateOrCreate([
                    'outlook_id' => $request->outlook_id,
                    'user_id' => $user->id,
                ]);
                Outlook::where('id', $request->outlook_id)->update([
                    'likes' => $getOutlook->likes + 1
                ]);
            } else {
                return response()->json(["alert" => "تم الاعجاب بالمنشور من قبل"], 404);
            }
        } else {
            return response()->json(["alert" => "المنشور غير موجود"], 404);
        }
    }

    public function delete_unused(Request $request)
    {
        $getExpired = Outlook::where('hide', '<=', date('Y-m-d H:i:s'))->get();
        foreach ($getExpired as $outlook) {
            if ($outlook->hide !== null && $outlook->hide !== '') {
                OutlookLikes::where('outlook_id', $outlook->id)->delete();
                OutlookShares::where('outlook_id', $outlook->id)->delete();
                $get_files = OutlooksFiles::where('outlook_id', $outlook->id)->get();
                foreach ($get_files as $file) {
                    File::delete(public_path() . '/storage/outlooks/' . $file->file);
                    OutlooksFiles::where('id', $file->id)->delete();
                }
                DB::table('outlooks_comments')->where('outlook_id', $outlook->id)->delete();
                Outlook::where('id', $outlook->id)->delete();
            }
        }
    }

    public function share(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        $getOutlook = Outlook::where('id', $request->outlook_id)->first();
        if ($getOutlook !== null) {
            OutlookShares::create([
                'outlook_id' => $request->outlook_id,
                'user_id' => $user->id,
            ]);
            Outlook::where('id', $request->outlook_id)->update([
                'shares' => $getOutlook->shares + 1
            ]);
        } else {
            return response()->json(["alert" => "المنشور غير موجود"], 404);
        }
    }

    /**
     * @param $dataDecode
     * @param Request $request
     */
    private function createPublicOutlookRecord($dataDecode, Request $request) : Outlook
    {
        $messaging = app('firebase.messaging');

        $getOutlook = Outlook::create([
            'title' => $dataDecode['title'],
            'date' => $dataDecode['date'],
            'country' => 'عام',
            'details' => $dataDecode['details'],
            'schedule' => $dataDecode['schedule'],
            'hide' => $dataDecode['hideDate'],
            'likes' => 0,
            'shares' => 0,
        ]);

        $messaging->send(
            \Kreait\Firebase\Messaging\CloudMessage::withTarget('topic', 'public')
                ->withNotification(\Kreait\Firebase\Messaging\Notification::create('توقع جديد', 'هناك توقع جديد عام'))
                ->withData($getOutlook->toArray())
        );

        foreach ($request->file('files') as $file) {
            $filename = uniqid() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('/outlooks/', $filename, 'public');
            OutlooksFiles::updateOrCreate([
                'outlook_id' => $getOutlook->id,
                'file' => $filename
            ]);
        }

        return $getOutlook;
    }

    /**
     * ❌ الدالة القديمة التي تحتوي على المشكلة - لا تستخدمها
     */
    private function getOutlooksArray(\Illuminate\Contracts\Pagination\LengthAwarePaginator $outlooks, Request $request): array
    {
        $items = collect($outlooks->items())->map(function ($outlook) use ($request) {
            $user = Users::where('token', $request->header('Authorization'))->first();
            $liked = false;

            if (!is_null($user)) {
                $liked = OutlookLikes::where('outlook_id', $request->outlook_id)->where('user_id', $user->id)->first();
            }

            $outlook['liked'] = $liked;

            return $outlook;
        });

        $outlooks = $outlooks->toArray();
        unset($outlooks['data']);
        $outlooks['data'] = $items->toArray();
        return $outlooks;
    }
}
