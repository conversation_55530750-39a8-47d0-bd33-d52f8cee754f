import 'package:flutter/material.dart';
import 'package:mattar/component/constants.dart';
import 'package:mattar/network/local/shared_pref.dart';

class StartingPage extends StatelessWidget {
  const StartingPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
      children: [
          const SizedBox(height: 50,),
          Image.asset(
            "images/start.jpg",
            color: Colors.white.withOpacity(0.5),
            colorBlendMode: BlendMode.modulate,
          ),
          // const SizedBox(
          //   height: 30,
          // ),
          Container(
              margin: const EdgeInsets.only(top: 70),
              width: 276,
              height: 99,
              decoration: BoxDecoration(
                  color: secondColor, borderRadius: BorderRadius.circular(12)),
              child: Center(
                child: Text(
                  textAlign: TextAlign.center,
                  "تطبيق مطر لمعرفة أحوال الطقس بالبلدان العربية",
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              )),
          const SizedBox(
            height: 10,
          ),
          Container(
            height: 40,
            width: 110,
            decoration: BoxDecoration(
                color: mainColor, borderRadius: BorderRadius.circular(12)),
            child: IconButton(
                onPressed: () {
                  CacheHelper.saveData(key: "onBoarding", value: false);
                  Navigator.of(context).pushReplacementNamed("country page2");
                },
                icon: const Icon(Icons.arrow_forward)),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
              width: 276,
              height: 99,
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(12)),
              child: Center(
                child: Column(
                  children: [
                    Text(
                      textAlign: TextAlign.center,
                      "تعلميات مهمة",style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: exitColor
                    ),
                     // style: Theme.of(context).textTheme.bodyText1,
                    ),
                    const Text(
                      textAlign: TextAlign.center,
                      "لتنقل بين المشاركات بالتطبيق",
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          color: Colors.black87,
                      ),
                    ),
                    const Text(
                      textAlign: TextAlign.center,
                      "قم بالسحب للأعلي والأسفل",
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          color: Colors.black87
                      ),
                    ),
                  ],
                ),
              )),
      ],
    ),
        ));
  }
}
