// ignore: file_names
class DeleteModel {
  DeleteModel({
      this.alert, 
      this.id, 
      this.type,});

  DeleteModel.fromJson(dynamic json) {
    alert = json['alert'];
    id = json['id'];
    type = json[' type'];
  }
  String? alert;
  String? id;
  String? type;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['alert'] = alert;
    map['id'] = id;
    map[' type'] = type;
    return map;
  }

}