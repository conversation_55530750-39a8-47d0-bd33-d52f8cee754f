name: mattar
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 34.0.0+34

environment:
  sdk: ">=3.4.3 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  shared_preferences: ^2.2.2
  flutter_bloc: ^8.1.3
  dio: ^5.3.3
  conditional_builder_null_safety: ^0.0.6
  readmore: ^2.2.0
  image_picker: ^1.0.4
  video_player: ^2.7.2
  toastification: ^2.3.0
  url_launcher: ^6.1.14
#  admob_flutter: ^3.0.0
  google_mobile_ads: ^4.0.0
  animated_splash_screen: ^1.3.0
  share_plus: ^7.2.2
  smooth_page_indicator: ^1.1.0
  connectivity_plus: ^5.0.1
  in_app_purchase: 3.1.8
  cached_network_image: ^3.3.0
  dropdown_button2: ^2.3.9
  timeago: ^3.7.0
  bottom_sheet: ^4.0.0
  modal_bottom_sheet: 3.0.0-pre
  freezed_annotation: ^2.4.1
  intl: ^0.19.0
#  firebase_analytics: ^10.6.1
  fl_chart: ^0.65.0
  flutter_html: ^3.0.0-beta.2
  webview_flutter: ^4.4.1
#  firebase_crashlytics: ^3.4.1
#  firebase_auth: ^4.11.1
  flutter_launcher_icons: ^0.13.1
  firebase_core: ^2.20.0

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "images/icon.png"
dev_dependencies:
  flutter_test:
    sdk: flutter
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "images/logo.png"

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.3
  build_runner: ^2.4.6
  freezed: ^2.4.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - images/
    #- assets/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
