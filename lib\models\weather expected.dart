import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:intl/intl.dart' as intl;
import 'package:mattar/component/constants.dart';
import 'package:mattar/models/mattar%20video%20and%20image/video.dart';
import 'package:mattar/models/signup.dart';
import 'package:mattar/network/local/shared_pref.dart';
import 'package:path/path.dart' as p;
import 'package:readmore/readmore.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

import '../component/component.dart';
import '../cubit/cubit/app_cubit.dart';
import 'ads/admob_service.dart';
import 'ads/ads_helper.dart';

// ignore: must_be_immutable
class WeatehrExpected extends StatefulWidget {
  WeatehrExpected({Key? key}) : super(key: key);

  @override
  State<WeatehrExpected> createState() => _WeatehrExpectedState();
}

class _WeatehrExpectedState extends State<WeatehrExpected> {
  TextEditingController commentController = TextEditingController();

  bool commentEntered = false;

  Future<void> share(
      String? title, String? text, String? url, String? chooserTitle) async {
    await Share.share(
        '${title ?? ""}\n${text ?? ""}\n${url ?? ""}',
        subject: chooserTitle);
  }

  @override
  void initState() {
    super.initState();
    // TODO: implement initState
    // AdmobService.homeBannerAd.load();
  }
  @override
  Widget build(BuildContext context) {
    PageController controller = PageController();

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => AppCubit()
            ..getWeatherPostes()
            ..getPrivateAds()
            ..initAds()
            ..getProfileData(),
        ),
        BlocProvider(create: (_) => CountriesCubit()),
      ],
      child: DefaultTabController(
        length: 2,
        child: BlocConsumer<AppCubit, ApppState>(
          listener: (context, state) {
            if (state is CommentSuccessfulState) {
              buildToast(text: "تم الارسال", color: Colors.black);
            }
            if (state is CommentErrorState) {
              buildToast(text: "تعذر الارسال ", color: Colors.black);
            }
            if (state is LikeSuccessfulState) {
              buildToast(text: "تم الاعجاب", color: Colors.black);
            }
            if (state is LikeErrorState) {
              buildToast(text: "تم الاعجاب من قبل", color: Colors.black);
            }
          },
          builder: (context, state) {
            var cubit = AppCubit.caller(context);

            List ind = [1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49];

            PageController photoController = PageController();
            return Scaffold(
              //   appBar: AppBar(
              //  elevation: 0,
              //   backgroundColor: Colors.white,
              // leadingWidth: double.infinity,
              // leading: TabBar(
              //   tabs: [
              //     const Tab(
              //       child: Text(
              //         "عام",
              //         style: TextStyle(
              //             color: Colors.black, fontWeight: FontWeight.w700),
              //       ),
              //     ),
              //      Tab(
              //         child: BlocBuilder<CountriesCubit, CountryModel?>(
              //           builder: (context, state) {
              //             return Row(
              //               mainAxisAlignment: MainAxisAlignment.center,
              //               children:  [
              //                 const Text(
              //                   "مخصص",
              //                   style: TextStyle(
              //                       color: Colors.black, fontWeight: FontWeight.w700),
              //                 ),
              //                 IconButton(onPressed: (){
              //                   Offset offs = Offset(0,0);
              //                   final RenderBox button = context.findRenderObject()! as RenderBox;
              //                   final RenderBox overlay = Navigator.of(context).overlay!.context.findRenderObject()! as RenderBox;
              //                   final RelativeRect position = RelativeRect.fromRect(
              //                     Rect.fromPoints(
              //                       button.localToGlobal(offs, ancestor: overlay),
              //                       button.localToGlobal(button.size.bottomRight(Offset.zero) + offs, ancestor: overlay),
              //                     ),
              //                     Offset.zero & overlay.size,
              //                   );
              //                   showMenu(context: context, position: position, items: [
              //                      PopupMenuItem(child: Text("fdf")),
              //                      PopupMenuItem(child: Text("fdf")),
              //                      PopupMenuItem(child: Text("fdf")),
              //                      PopupMenuItem(child: Text("fdf")),
              //                      PopupMenuItem(child: Text("fdf")),
              //                   ]);
              //                 }, icon: Icon(Icons.arrow_drop_down,color: Colors.black,))
              //               ],
              //             );
              //           },
              //         ),
              //      ),
              //   ],
              //   indicatorColor: const Color.fromRGBO(
              //       66, 105, 129, 1),
              // ),
              //   ),
              body: PageView.builder(
                  controller: controller,
                  scrollDirection: Axis.vertical,
                  itemCount: cubit.posts.length,
                  pageSnapping: true,
                  itemBuilder: (ctx, index) {

                    final adds = cubit.posts[index].bannerAdH1;
                    if(adds == null) {
                      cubit.posts[index].bannerAdH1 = BannerAd(
                        adUnitId: AdmobServiceNew.adUnitIdBanner,
                        request: const AdRequest(),
                        size: AdSize(width: MediaQuery.of(context).size.width.toInt(), height: 80),
                        listener: BannerAdListener(
                          // Called when an ad is successfully received.
                          onAdLoaded: (ad) {
                            debugPrint('${ad.adUnitId} loaded from new .');
                            setState(() {
                              cubit.posts[index].addLoaded = true;
                            });
                          },
                          // Called when an ad request failed.
                          onAdFailedToLoad: (ad, err) {
                            debugPrint('BannerAd failed to load: $err');
                            // Dispose the ad here to free resources.
                            ad.dispose();
                          },
                        ),
                      )
                        ..load();
                    }

                    final adds2 = cubit.posts[index].bannerAdH2;
                    if(adds2 == null) {
                      final size = MediaQuery.of(context).size;
                      cubit.posts[index].bannerAdH2 = BannerAd(
                        adUnitId: AdmobServiceNew.adUnitIdBanner,
                        request: const AdRequest(),
                        size: AdSize(width: size.width.toInt(), height: size
                            .height.toInt() - 200),
                        listener: BannerAdListener(
                          // Called when an ad is successfully received.
                          onAdLoaded: (ad) {
                            debugPrint('${ad.adUnitId} loaded from new 2.');
                            setState(() {
                              cubit.posts[index].addLoaded2 = true;
                            });
                          },
                          // Called when an ad request failed.
                          onAdFailedToLoad: (ad, err) {
                            debugPrint('BannerAd failed to load2: $err');
                            // Dispose the ad here to free resources.
                            ad.dispose();
                          },
                        ),
                      )
                        ..load();
                    }

                    return Scaffold(
                        body: ind.contains(index) && cubit.profile?.sub != true
                            ? PageView.builder(
                                itemCount: 1,
                                itemBuilder: (c, n) {
                                  return cubit.adsModel.isNotEmpty && index == 1
                                      ? Column(
                                          children: [
                                            const SizedBox(height: 20),
                                            Text(cubit.adsModel[n].title,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .displayLarge
                                                    ?.copyWith(
                                                        color:
                                                            Colors.grey[600])),
                                            Expanded(
                                              child: Container(
                                                color: Colors.white,
                                                margin:
                                                    const EdgeInsets.all(10),
                                                child: Image.network(
                                                    "https://admin.rain-app.com/storage/ads/${cubit.adsModel[n].media}",
                                                    errorBuilder:
                                                        (BuildContext context,
                                                            Object exception,
                                                            StackTrace?
                                                                stackTrace) {
                                                  return const Text(
                                                      'Your error widget...');
                                                }),
                                              ),
                                            ),
                                            defaultButton(
                                                onPressed: () async {
                                                  cubit.increaseAdsClick(
                                                      cubit.adsModel[n].id);
                                                  try {
                                                    await canLaunch(cubit
                                                            .adsModel[n]
                                                            .redirect)
                                                        ? await launch(cubit
                                                            .adsModel[n]
                                                            .redirect)
                                                        : throw "could not fiend";
                                                  } catch (e) {
                                                    return;
                                                  }
                                                },
                                                textButton: "اذهب",
                                                width: 150,
                                                radius: 15),
                                            const SizedBox(
                                              height: 10,
                                            )
                                          ],
                                        )
                                      : Column(
                                          children: [
                                            // Flexible(
                                            //   flex: 0,
                                            //   child: defaultButton(
                                            //       onPressed: () {
                                            //         Navigator.of(context)
                                            //             .pushNamed("ads");
                                            //       },
                                            //       textButton:
                                            //           "الغاء الاعلانات"),
                                            // ),
                                            if(cubit.posts[index].addLoaded2 ?? false)
                                              Flexible(
                                                child: Center(
                                                  child: SizedBox(
                                                    width: adds2!.size.width.toDouble(),
                                                    height: adds2.size.height.toDouble(),
                                                    child: AdWidget(ad: adds2),
                                                  )
                                                ),
                                              ),
                                          ],
                                        );
                                })
                            : Column(children: [
                                Stack(children: [
                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.35,
                                      // color: Colors.black,
                                      child: PageView.builder(
                                          controller: photoController,
                                          scrollDirection: Axis.horizontal,
                                          itemCount:
                                              cubit.posts[index].files.length,
                                          itemBuilder: (ctx, photoind) {
                                            final extension = p.extension(cubit
                                                .posts[index]
                                                .files[photoind]
                                                .file);

                                            return extension == ".mp4"
                                                ? SizedBox(
                                                    width: double.infinity,
                                                    height:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.5,
                                                    child: PlayV(
                                                      "https://admin.rain-app.com/storage/outlooks/${cubit.posts[index].files[photoind].file}",
                                                    ))
                                                : SizedBox(
                                                    width: double.infinity,
                                                    height:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.35,
                                                    child: InteractiveViewer(
                                                      child: Image.network(
                                                        "https://admin.rain-app.com/storage/outlooks/${cubit.posts[index].files[photoind].file}",
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (context,
                                                            error, stackTrace) {
                                                          return const Text(
                                                              "can not load image");
                                                        },
                                                      ),
                                                    ));
                                          })),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(top: 8),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 5, horizontal: 20),
                                        height: 50,
                                        decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                66, 105, 129, 1),
                                            borderRadius:
                                                BorderRadius.circular(12)),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceEvenly,
                                          children: [
                                            Text(
                                              "${CacheHelper.getData(key: "country")}",
                                              style: const TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            Image.network(
                                              "https://admin.rain-app.com/storage/countries/${CacheHelper.getData(key: "country")}.png",
                                              height: 50,
                                            ),
                                          ],
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Navigator.of(context)
                                              .pushReplacementNamed(
                                                  "country page");
                                        },
                                        child: Container(
                                          margin: const EdgeInsets.only(top: 8),
                                          width: 120,
                                          height: 50,
                                          decoration: BoxDecoration(
                                              color: whiteColor,
                                              borderRadius:
                                                  BorderRadius.circular(12)),
                                          child: const Center(
                                            child: Text(
                                              "تغيير القسم",
                                              style: TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  cubit.posts[index].files.length > 1
                                      ? SizedBox(
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.34,
                                          child: Align(
                                            alignment: Alignment.bottomCenter,
                                            child: SmoothPageIndicator(
                                                axisDirection: Axis.horizontal,
                                                effect: const WormEffect(
                                                    dotHeight: 12,
                                                    dotWidth: 12),
                                                controller: photoController,
                                                count: cubit
                                                    .posts[index].files.length),
                                          ),
                                        )
                                      : const SizedBox()
                                ]),
                                Container(
                                  decoration: const BoxDecoration(
                                      color: Color.fromRGBO(66, 105, 129, 1)),
                                  height: 42,
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10),
                                  // color: const Color.fromRGBO(66, 105, 129, 1),
                                  child: FittedBox(
                                    fit: BoxFit.cover,
                                    child: Row(
                                      children: [
                                        defaultWeatherExpectedRowIcon(
                                            text: cubit.posts[index].date,
                                            icon: Icons.access_time_rounded),
                                        SizedBox(width: 30,),
                                        defaultWeatherExpectedRowIcon(
                                          onPressed: () async {
                                            if (CacheHelper.getData(
                                                    key: "login") ==
                                                null) {
                                              await LogInDialog(context);
                                              return;
                                            }
                                            cubit.posts[index].liked = true;
                                            cubit.sendLike(
                                                outlookId:
                                                    cubit.posts[index].id);
                                          },
                                          child: cubit.posts[index].liked!
                                              ? const Icon(
                                                  Icons.favorite,
                                                  color: Colors.redAccent,
                                                  size: 20,
                                                )
                                              : const Icon(
                                                  Icons.favorite_border,
                                                  color: Colors.white,
                                                  size: 20,
                                                ),
                                        ),
                                        // defaultWeatherExpectedRowIcon(
                                        //     icon: Icons.comment,
                                        //     onPressed: () {
                                        //       showFlexibleBottomSheet(
                                        //         minHeight: 0,
                                        //         initHeight: 0.5,
                                        //         maxHeight: 1,
                                        //         bottomSheetColor:
                                        //             Colors.transparent,
                                        //         context: context,
                                        //         builder: (context,
                                        //                 scrollController,
                                        //                 offset) =>
                                        //             _buildBottomSheet(
                                        //                 context,
                                        //                 scrollController,
                                        //                 cubit,
                                        //                 index),
                                        //         anchors: [0, 0.5, 1],
                                        //         isSafeArea: true,
                                        //       );
                                        //     }),
                                        defaultWeatherExpectedRowIcon(
                                          onPressed: () {
                                            cubit.sendShare(
                                                outlookId:
                                                    cubit.posts[index].id);
                                            share(
                                                cubit.posts[index].title,
                                                cubit.posts[index].details,
                                                "https://rain-app.com/outlook/${cubit.posts[index].id}",
                                                "");
                                          },
                                          icon: Icons.share,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                cubit.profile?.sub != true && (cubit.posts[index].addLoaded ?? false) ?
                                Align(
                                  alignment: Alignment.bottomCenter,
                                  child: SafeArea(
                                    child: SizedBox(
                                      width: adds!.size.width.toDouble(),
                                      height: adds.size.height.toDouble(),
                                      child: AdWidget(ad: adds),
                                    ),
                                  ),
                                ) : const SizedBox() ,
                    //             cubit.profile?.sub != true
                    //                 ?
                    // SizedBox(
                    // width:
                    // AdmobService.homeBannerAd.size.width.toDouble(),
                    // height:
                    // AdmobService.homeBannerAd.size.height.toDouble(),
                    // child: AdWidget(
                    // ad: AdmobService.homeBannerAd,
                    // ),
                    // )
                    //
                    //                 : const SizedBox(),
                                const SizedBox(
                                  height: 10,
                                ),

                                /// description
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: backgroundColor,
                                    ),
                                    //  color: backgroundColor,
                                    margin: const EdgeInsets.symmetric(
                                        vertical: 2, horizontal: 8),
                                    child: Container(
                                      padding: const EdgeInsets.all(10),
                                      width: double.infinity,
                                      margin: const EdgeInsets.only(bottom: 15),
                                      decoration: BoxDecoration(
                                          color: whiteColor,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          boxShadow: [shadow()]),
                                      child: SingleChildScrollView(
                                        child: Column(
                                          children: [
                                            cubit.posts[index].title != ""
                                                ? Text(cubit.posts[index].title,
                                                    style: const TextStyle(
                                                        fontSize: 24,
                                                        color: Colors.pink,
                                                        fontWeight:
                                                            FontWeight.w700))
                                                : const SizedBox(),
                                            ReadMoreText(
                                              cubit.posts[index].details,
                                              colorClickableText: Colors.pink,
                                              trimMode: TrimMode.Line,
                                              style: const TextStyle(
                                                  fontSize: 22,
                                                  fontWeight: FontWeight.w700),
                                              trimLines: 4,
                                              trimCollapsedText: 'قراءة المزيد',
                                              trimExpandedText: 'قراءة اقل',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ]));
                  }),
              // PageView.builder(
              //     controller: controller,
              //     scrollDirection: Axis.vertical,
              //     itemCount: cubit.posts.length,
              //     pageSnapping: true,
              //     itemBuilder: (ctx, index) {
              //       return Scaffold(
              //           body: ind.contains(index) &&
              //                   cubit.profile?.sub != true
              //               ? PageView.builder(
              //                   itemCount: 1,
              //                   itemBuilder: (c, n) {
              //                     return cubit.adsModel.isNotEmpty &&
              //                             index == 1
              //                         ? Column(
              //                             children: [
              //                               const SizedBox(height: 20),
              //                               Text(cubit.adsModel[n].title,
              //                                   style: Theme.of(context)
              //                                       .textTheme
              //                                       .headline1
              //                                       ?.copyWith(
              //                                           color: Colors
              //                                               .grey[600])),
              //                               Expanded(
              //                                 child: Container(
              //                                   color: Colors.white,
              //                                   margin:
              //                                       const EdgeInsets.all(
              //                                           10),
              //                                   child: Image.network(
              //                                       "https://admin.rain-app.com/storage/ads/${cubit.adsModel[n].media}",
              //                                       errorBuilder:
              //                                           (BuildContext
              //                                                   context,
              //                                               Object
              //                                                   exception,
              //                                               StackTrace?
              //                                                   stackTrace) {
              //                                     return const Text(
              //                                         'Your error widget...');
              //                                   }),
              //                                 ),
              //                               ),
              //                               defaultButton(
              //                                   onPressed: () async {
              //                                     cubit.increaseAdsClick(
              //                                         cubit.adsModel[n].id);
              //                                     try {
              //                                       await canLaunch(cubit
              //                                               .adsModel[n]
              //                                               .redirect)
              //                                           ? await launch(cubit
              //                                               .adsModel[n]
              //                                               .redirect)
              //                                           : throw "could not fiend";
              //                                     } catch (e) {
              //                                       return;
              //                                     }
              //                                   },
              //                                   textButton: "اذهب",
              //                                   width: 150,
              //                                   radius: 15),
              //                               const SizedBox(
              //                                 height: 10,
              //                               )
              //                             ],
              //                           )
              //                         : Column(
              //                             children: [
              //                               Flexible(
              //                                 flex: 0,
              //                                 child: defaultButton(
              //                                     onPressed: () {
              //                                       Navigator.of(context)
              //                                           .pushNamed("ads");
              //                                     },
              //                                     textButton:
              //                                         "الغاء الاعلانات"),
              //                               ),
              //                               Flexible(
              //                                 child:
              //                                     Builder(builder: (ctx) {
              //                                   return Container(
              //                                     decoration: BoxDecoration(
              //                                         border: Border.all(
              //                                             color:
              //                                                 Colors.pink)),
              //                                     width: double.infinity,
              //                                     child: Center(
              //                                       child: AdmobBanner(
              //                                           adUnitId: AdsHelper
              //                                               .getBunnerAd(),
              //                                           adSize:
              //                                               AdmobBannerSize
              //                                                   .SMART_BANNER(
              //                                                       ctx)),
              //                                     ),
              //                                   );
              //                                 }),
              //                               ),
              //                             ],
              //                           );
              //                   })
              //               : Column(children: [
              //                   Stack(children: [
              //                     SizedBox(
              //                         height: MediaQuery.of(context)
              //                                 .size
              //                                 .height *
              //                             0.35,
              //                         // color: Colors.black,
              //                         child: PageView.builder(
              //                             controller: photoController,
              //                             scrollDirection: Axis.horizontal,
              //                             itemCount: cubit
              //                                 .posts[index].files.length,
              //                             itemBuilder: (ctx, photoind) {
              //                               final extension = p.extension(
              //                                   cubit.posts[index]
              //                                       .files[photoind].file);
              //
              //                               return extension == ".mp4"
              //                                   ? Container(
              //                                       width: double.infinity,
              //                                       height: MediaQuery.of(
              //                                                   context)
              //                                               .size
              //                                               .height *
              //                                           0.5,
              //                                       child: PlayV(
              //                                         "https://admin.rain-app.com/storage/outlooks/${cubit.posts[index].files[photoind].file}",
              //                                       ))
              //                                   : Container(
              //                                       width: double.infinity,
              //                                       height: MediaQuery.of(
              //                                                   context)
              //                                               .size
              //                                               .height *
              //                                           0.35,
              //                                       child:
              //                                           InteractiveViewer(
              //                                         child: Image.network(
              //                                           "https://admin.rain-app.com/storage/outlooks/${cubit.posts[index].files[photoind].file}",
              //                                           fit: BoxFit.cover,
              //                                           errorBuilder:
              //                                               (context, error,
              //                                                   stackTrace) {
              //                                             return Text(
              //                                                 "can not load image");
              //                                           },
              //                                         ),
              //                                       ));
              //                             })),
              //                     Row(
              //                       mainAxisAlignment:
              //                           MainAxisAlignment.spaceEvenly,
              //                       children: [
              //                         Container(
              //                           margin:
              //                               const EdgeInsets.only(top: 8),
              //                           padding: const EdgeInsets.symmetric(
              //                               vertical: 5, horizontal: 20),
              //                           height: 50,
              //                           decoration: BoxDecoration(
              //                               color: const Color.fromRGBO(
              //                                   66, 105, 129, 1),
              //                               borderRadius:
              //                                   BorderRadius.circular(12)),
              //                           child: Row(
              //                             mainAxisAlignment:
              //                                 MainAxisAlignment.spaceEvenly,
              //                             children: [
              //                               Text(
              //                                 "${CacheHelper.getData(key: "country")}",
              //                                 style: const TextStyle(
              //                                     fontSize: 16,
              //                                     color: Colors.white,
              //                                     fontWeight:
              //                                         FontWeight.bold),
              //                               ),
              //                               Image.network(
              //                                 "https://admin.rain-app.com/storage/countries/${CacheHelper.getData(key: "country")}.png",
              //                                 height: 50,
              //                               ),
              //                             ],
              //                           ),
              //                         ),
              //                         InkWell(
              //                           onTap: () {
              //                             Navigator.of(context)
              //                                 .pushReplacementNamed(
              //                                     "country page");
              //                           },
              //                           child: Container(
              //                             margin:
              //                                 const EdgeInsets.only(top: 8),
              //                             width: 120,
              //                             height: 50,
              //                             decoration: BoxDecoration(
              //                                 color: whiteColor,
              //                                 borderRadius:
              //                                     BorderRadius.circular(
              //                                         12)),
              //                             child: const Center(
              //                               child: Text(
              //                                 "تغيير القسم",
              //                                 style: TextStyle(
              //                                     color: Colors.black,
              //                                     fontSize: 16,
              //                                     fontWeight:
              //                                         FontWeight.bold),
              //                               ),
              //                             ),
              //                           ),
              //                         ),
              //                       ],
              //                     ),
              //                     cubit.posts[index].files.length > 1
              //                         ? SizedBox(
              //                             height: MediaQuery.of(context)
              //                                     .size
              //                                     .height *
              //                                 0.34,
              //                             child: Align(
              //                               alignment:
              //                                   Alignment.bottomCenter,
              //                               child: SmoothPageIndicator(
              //                                   axisDirection:
              //                                       Axis.horizontal,
              //                                   effect: const WormEffect(
              //                                       dotHeight: 12,
              //                                       dotWidth: 12),
              //                                   controller: photoController,
              //                                   count: cubit.posts[index]
              //                                       .files.length),
              //                             ),
              //                           )
              //                         : const SizedBox()
              //                   ]),
              //                   Container(
              //                     decoration: const BoxDecoration(
              //                         color:
              //                             Color.fromRGBO(66, 105, 129, 1)),
              //                     height: 42,
              //                     width: double.infinity,
              //                     padding: const EdgeInsets.symmetric(
              //                         horizontal: 10),
              //                     // color: const Color.fromRGBO(66, 105, 129, 1),
              //                     child: FittedBox(
              //                       fit: BoxFit.cover,
              //                       child: Row(
              //                         children: [
              //                           defaultWeatherExpectedRowIcon(
              //                               text: cubit.posts[index].date,
              //                               icon:
              //                                   Icons.access_time_rounded),
              //                           defaultWeatherExpectedRowIcon(
              //                             onPressed: () async {
              //                               if (CacheHelper.getData(
              //                                       key: "login") ==
              //                                   null) {
              //                                 await LogInDialog(context);
              //                                 return;
              //                               }
              //                               cubit.posts[index].liked = true;
              //                               cubit.sendLike(
              //                                   outlookId:
              //                                       cubit.posts[index].id);
              //                             },
              //                             child: cubit.posts[index].liked!
              //                                 ? const Icon(
              //                                     Icons.favorite,
              //                                     color: Colors.redAccent,
              //                                     size: 25,
              //                                   )
              //                                 : const Icon(
              //                                     Icons.favorite_border,
              //                                     color: Colors.white,
              //                                     size: 25,
              //                                   ),
              //                           ),
              //                           defaultWeatherExpectedRowIcon(
              //                               icon: Icons.comment,
              //                               onPressed: () {
              //                                 showFlexibleBottomSheet(
              //                                   minHeight: 0,
              //                                   initHeight: 0.5,
              //                                   maxHeight: 1,
              //                                   bottomSheetColor:
              //                                       Colors.transparent,
              //                                   context: context,
              //                                   builder: (context,
              //                                           scrollController,
              //                                           offset) =>
              //                                       _buildBottomSheet(
              //                                           context,
              //                                           scrollController,
              //                                           cubit,
              //                                           index),
              //                                   anchors: [0, 0.5, 1],
              //                                   isSafeArea: true,
              //                                 );
              //                               }),
              //                           defaultWeatherExpectedRowIcon(
              //                             onPressed: () {
              //                               cubit.sendShare(
              //                                   outlookId:
              //                                       cubit.posts[index].id);
              //                               share(
              //                                   cubit.posts[index].title,
              //                                   cubit.posts[index].details,
              //                                   "https://rain-app.com/outlook/${cubit.posts[index].id}",
              //                                   "");
              //                             },
              //                             icon: Icons.share,
              //                           ),
              //                         ],
              //                       ),
              //                     ),
              //                   ),
              //                   cubit.profile?.sub != true
              //                       ? AdmobBanner(
              //                           adUnitId: AdsHelper.getBunnerAd(),
              //                           adSize: AdmobBannerSize.FULL_BANNER)
              //                       : const SizedBox(),
              //                   const SizedBox(
              //                     height: 10,
              //                   ),
              //
              //                   /// description
              //                   Expanded(
              //                     child: Container(
              //                       decoration: BoxDecoration(
              //                         color: backgroundColor,
              //                       ),
              //                       //  color: backgroundColor,
              //                       margin: const EdgeInsets.symmetric(
              //                           vertical: 2, horizontal: 8),
              //                       child: Container(
              //                         padding: const EdgeInsets.all(10),
              //                         width: double.infinity,
              //                         margin: EdgeInsets.only(bottom: 15),
              //                         decoration: BoxDecoration(
              //                             color: whiteColor,
              //                             borderRadius:
              //                                 BorderRadius.circular(12),
              //                             boxShadow: [shadow()]),
              //                         child: SingleChildScrollView(
              //                           child: Column(
              //                             children: [
              //                               cubit.posts[index].title != ""
              //                                   ? Text(
              //                                       cubit
              //                                           .posts[index].title,
              //                                       style: const TextStyle(
              //                                           fontSize: 24,
              //                                           color: Colors.pink,
              //                                           fontWeight:
              //                                               FontWeight
              //                                                   .w700))
              //                                   : const SizedBox(),
              //                               ReadMoreText(
              //                                 cubit.posts[index].details,
              //                                 colorClickableText:
              //                                     Colors.pink,
              //                                 trimMode: TrimMode.Line,
              //                                 style: const TextStyle(
              //                                     fontSize: 22,
              //                                     fontWeight:
              //                                         FontWeight.w700),
              //                                 trimLines: 4,
              //                                 trimCollapsedText:
              //                                     'قراءة المزيد',
              //                                 trimExpandedText: 'قراءة اقل',
              //                               ),
              //                             ],
              //                           ),
              //                         ),
              //                       ),
              //                     ),
              //                   )
              //                 ]));
              //     }),
            );
            // return PageView.builder(
            //     controller: controller,
            //     scrollDirection: Axis.vertical,
            //     itemCount: cubit.posts.length,
            //     pageSnapping: true,
            //     itemBuilder: (ctx, index) {
            //       return Scaffold(
            //         body: ind.contains(index) && cubit.profile?.sub != true
            //             ? PageView.builder(
            //                 itemCount: 1,
            //                 itemBuilder: (c, n) {
            //                   return cubit.adsModel.isNotEmpty && index == 1
            //                       ? Column(
            //                           children: [
            //                             const SizedBox(height: 20),
            //                             Text(cubit.adsModel[n].title,
            //                                 style: Theme.of(context)
            //                                     .textTheme
            //                                     .headline1
            //                                     ?.copyWith(
            //                                         color: Colors.grey[600])),
            //                             Expanded(
            //                               child: Container(
            //                                 color: Colors.white,
            //                                 margin: const EdgeInsets.all(10),
            //                                 child: Image.network(
            //                                     "https://admin.rain-app.com/storage/ads/${cubit.adsModel[n].media}",
            //                                     errorBuilder:
            //                                         (BuildContext context,
            //                                             Object exception,
            //                                             StackTrace? stackTrace) {
            //                                   return const Text(
            //                                       'Your error widget...');
            //                                 }),
            //                               ),
            //                             ),
            //                             defaultButton(
            //                                 onPressed: () async {
            //                                   cubit.increaseAdsClick(
            //                                       cubit.adsModel[n].id);
            //                                   try {
            //                                     await canLaunch(cubit
            //                                             .adsModel[n].redirect)
            //                                         ? await launch(cubit
            //                                             .adsModel[n].redirect)
            //                                         : throw "could not fiend";
            //                                   } catch (e) {
            //                                     return;
            //                                   }
            //                                 },
            //                                 textButton: "اذهب",
            //                                 width: 150,
            //                                 radius: 15),
            //                             const SizedBox(
            //                               height: 10,
            //                             )
            //                           ],
            //                         )
            //                       : Column(
            //                           children: [
            //                             Flexible(
            //                               flex: 0,
            //                               child: defaultButton(
            //                                   onPressed: () {
            //                                     Navigator.of(context)
            //                                         .pushNamed("ads");
            //                                   },
            //                                   textButton: "الغاء الاعلانات"),
            //                             ),
            //                             Flexible(
            //                               child: Builder(
            //                                 builder: (ctx) {
            //                                   return Container(
            //                                     decoration: BoxDecoration(
            //                                       border: Border.all(color: Colors.pink)
            //                                     ),
            //                                     width: double.infinity,
            //                                     child: Center(
            //                                       child: AdmobBanner(
            //                                           adUnitId: AdsHelper.getBunnerAd(),
            //                                           adSize: AdmobBannerSize
            //                                               .SMART_BANNER(ctx)),
            //                                     ),
            //                                   );
            //                                 }
            //                               ),
            //                             ),
            //                           ],
            //                         );
            //                 })
            //             : Column(children: [
            //           Stack(children: [
            //             SizedBox(
            //
            //                 height:
            //                 MediaQuery.of(context).size.height * 0.35,
            //                 // color: Colors.black,
            //                 child: PageView.builder(
            //                     controller: photoController,
            //                     scrollDirection: Axis.horizontal,
            //                     itemCount:
            //                     cubit.posts[index].files.length,
            //                     itemBuilder: (ctx, photoind) {
            //                       final extension = p.extension(cubit
            //                           .posts[index].files[photoind].file);
            //
            //                       return extension == ".mp4"
            //                           ? Container(
            //                           width: double.infinity,
            //                           height: MediaQuery.of(context)
            //                               .size
            //                               .height *
            //                               0.5,
            //                           child: PlayV(
            //                             "https://admin.rain-app.com/storage/outlooks/${cubit.posts[index].files[photoind].file}",
            //                           ))
            //                           : Container(
            //                           width: double.infinity,
            //                           height: MediaQuery.of(context)
            //                               .size
            //                               .height *
            //                               0.35,
            //                           child: InteractiveViewer(
            //                             child: Image.network(
            //                               "https://admin.rain-app.com/storage/outlooks/${cubit.posts[index].files[photoind].file}",
            //                               fit: BoxFit.cover,
            //                               errorBuilder: (context,
            //                                   error, stackTrace) {
            //                                 return Text(
            //                                     "can not load image");
            //                               },
            //                             ),
            //                           ));
            //                     })),
            //             Row(
            //               mainAxisAlignment:
            //               MainAxisAlignment.spaceEvenly,
            //               children: [
            //                 Container(
            //                   margin: const EdgeInsets.only(top: 8),
            //                   padding: const EdgeInsets.symmetric(
            //                       vertical: 5, horizontal: 20),
            //                   height: 50,
            //                   decoration: BoxDecoration(
            //                       color: const Color.fromRGBO(
            //                           66, 105, 129, 1),
            //                       borderRadius:
            //                       BorderRadius.circular(12)),
            //                   child: Row(
            //                     mainAxisAlignment:
            //                     MainAxisAlignment.spaceEvenly,
            //                     children: [
            //                       Text(
            //                         "${CacheHelper.getData(key: "country")}",
            //                         style: const TextStyle(
            //                             fontSize: 16,
            //                             color: Colors.white,
            //                             fontWeight: FontWeight.bold),
            //                       ),
            //                       Image.network(
            //                         "https://admin.rain-app.com/storage/countries/${CacheHelper.getData(key: "country")}.png",
            //                         height: 50,
            //                       ),
            //                     ],
            //                   ),
            //                 ),
            //                 InkWell(
            //                   onTap: () {
            //                     Navigator.of(context)
            //                         .pushReplacementNamed("country page");
            //                   },
            //                   child: Container(
            //                     margin: const EdgeInsets.only(top: 8),
            //                     width: 120,
            //                     height: 50,
            //                     decoration: BoxDecoration(
            //                         color: whiteColor,
            //                         borderRadius:
            //                         BorderRadius.circular(12)),
            //                     child: const Center(
            //                       child: Text(
            //                         "تغيير القسم",
            //                         style: TextStyle(
            //                             color: Colors.black,
            //                             fontSize: 16,
            //                             fontWeight: FontWeight.bold),
            //                       ),
            //                     ),
            //                   ),
            //                 ),
            //               ],
            //             ),
            //             cubit.posts[index].files.length > 1
            //                 ? SizedBox(
            //
            //               height:
            //               MediaQuery.of(context).size.height *
            //                   0.34,
            //               child: Align(
            //                 alignment: Alignment.bottomCenter,
            //                 child: SmoothPageIndicator(
            //                     axisDirection: Axis.horizontal,
            //                     effect: const WormEffect(
            //                         dotHeight: 12, dotWidth: 12),
            //                     controller: photoController,
            //                     count: cubit
            //                         .posts[index].files.length),
            //               ),
            //             )
            //                 : const SizedBox()
            //           ]),
            //           Container(
            //             decoration: const BoxDecoration(
            //                 color: Color.fromRGBO(66, 105, 129, 1)),
            //             height: 42,
            //             width: double.infinity,
            //             padding: const EdgeInsets.symmetric(horizontal: 10),
            //             // color: const Color.fromRGBO(66, 105, 129, 1),
            //             child: FittedBox(
            //               fit: BoxFit.cover,
            //               child: Row(
            //                 children: [
            //                   defaultWeatherExpectedRowIcon(
            //                       text: cubit.posts[index].date,
            //                       icon: Icons.access_time_rounded),
            //                   defaultWeatherExpectedRowIcon(
            //                     onPressed: () async {
            //                       if (CacheHelper.getData(key: "login") ==
            //                           null) {
            //                         await LogInDialog(context);
            //                         return;
            //                       }
            //                       cubit.posts[index].liked = true;
            //                       cubit.sendLike(
            //                           outlookId: cubit.posts[index].id);
            //                     },
            //                     child: cubit.posts[index].liked!
            //                         ? const Icon(
            //                       Icons.favorite,
            //                       color: Colors.redAccent,
            //                       size: 25,
            //                     )
            //                         : const Icon(
            //                       Icons.favorite_border,
            //                       color: Colors.white,
            //                       size: 25,
            //                     ),
            //                   ),
            //                   defaultWeatherExpectedRowIcon(
            //                     icon: Icons.comment,
            //                     onPressed: () {
            //                       showFlexibleBottomSheet(
            //                         minHeight: 0,
            //                         initHeight: 0.5,
            //                         maxHeight: 1,
            //                         bottomSheetColor:
            //                             Colors.transparent,
            //                         context: context,
            //                         builder: (context, scrollController,
            //                                 offset) =>
            //                             _buildBottomSheet(
            //                                 context,
            //                                 scrollController,
            //                                 cubit,
            //                                 index),
            //                         anchors: [0, 0.5, 1],
            //                         isSafeArea: true,
            //                       );
            //                     }
            //                   ),
            //                   defaultWeatherExpectedRowIcon(
            //                     onPressed: () {
            //                       cubit.sendShare(
            //                           outlookId: cubit.posts[index].id);
            //                       share(
            //                           cubit.posts[index].title,
            //                           cubit.posts[index].details,
            //                           "https://rain-app.com/outlook/${cubit.posts[index].id}",
            //                           "");
            //                     },
            //                     icon: Icons.share,
            //                   ),
            //                 ],
            //               ),
            //             ),
            //           ),
            //           cubit.profile?.sub != true
            //               ? AdmobBanner(
            //               adUnitId: AdsHelper.getBunnerAd(),
            //               adSize: AdmobBannerSize.FULL_BANNER)
            //               : const SizedBox(),
            //           const SizedBox(
            //             height: 10,
            //           ),
            //           /// description
            //           Expanded(
            //             child: Container(
            //               decoration: BoxDecoration(
            //                 color: backgroundColor,
            //               ),
            //               //  color: backgroundColor,
            //               margin: const EdgeInsets.symmetric(
            //                   vertical: 2, horizontal: 8),
            //               child:
            //               Container(
            //                 padding: const EdgeInsets.all(10),
            //                 width: double.infinity,
            //                 margin: EdgeInsets.only(bottom: 15),
            //                 decoration: BoxDecoration(
            //                     color: whiteColor,
            //                     borderRadius:
            //                     BorderRadius.circular(12),
            //                     boxShadow: [shadow()]),
            //                 child: SingleChildScrollView(
            //                   child: Column(
            //                     children: [
            //                       cubit.posts[index].title != ""
            //                           ? Text(
            //                           cubit.posts[index].title,
            //                           style: const TextStyle(
            //                               fontSize: 24,
            //                               color: Colors.pink,
            //                               fontWeight:
            //                               FontWeight.w700))
            //                           : const SizedBox(),
            //                       ReadMoreText(
            //                         cubit.posts[index].details,
            //                         colorClickableText: Colors.pink,
            //                         trimMode: TrimMode.Line,
            //                         style: const TextStyle(
            //                             fontSize: 22,
            //                             fontWeight:
            //                             FontWeight.w700),
            //                         trimLines: 4,
            //                         trimCollapsedText:
            //                         'قراءة المزيد',
            //                         trimExpandedText: 'قراءة اقل',
            //                       ),
            //                     ],
            //                   ),
            //                 ),
            //               )
            //               ,
            //             ),
            //           )
            //         ])
            //       );
            //     });
          },
        ),
      ),
    );
  }

  Widget _buildBottomSheet(BuildContext context,
      ScrollController scrollController, AppCubit cubit, int postIndex) {
    var commentsAndReplay = cubit.posts[postIndex].comments
        .where(((element) =>
            element.user.token == CacheHelper.getData(key: "token")))
        .toList();

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20), topLeft: Radius.circular(20)),
        color: Colors.grey.shade200,
      ),
      child: GestureDetector(
        onVerticalDragDown: (_) {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Flexible(
              flex: 0,
              child: Text(
                "التعليقات",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
              ),
            ),
            cubit.profile?.role == "admin"
                ? Flexible(
                    flex: 1,
                    child: Container(
                      margin: const EdgeInsets.only(top: 10, bottom: 10),
                      child: ListView.builder(
                          controller: scrollController,
                          physics: const BouncingScrollPhysics(),
                          itemCount: cubit.posts[postIndex].comments.length,
                          itemBuilder: (__, ind) {
                            return Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: 40,
                                      width: 40,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(200)),
                                      child: ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(200),
                                        child: Image.asset("images/avatar.png"),
                                      ),
                                    ),
                                    Flexible(
                                      fit: FlexFit.tight,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                              margin: const EdgeInsets.only(
                                                  right: 8),
                                              padding: const EdgeInsets.all(4),
                                              decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                      cubit
                                                          .posts[postIndex]
                                                          .comments[ind]
                                                          .user
                                                          .name,
                                                      style: const TextStyle(
                                                          fontSize: 20,
                                                          color: Color.fromRGBO(
                                                              66,
                                                              105,
                                                              129,
                                                              1))),
                                                  Text(
                                                      cubit
                                                          .posts[postIndex]
                                                          .comments[ind]
                                                          .comment,
                                                      style: const TextStyle(
                                                          color: Colors.black,
                                                          fontSize: 18)),
                                                ],
                                              )),
                                          Container(
                                            margin: const EdgeInsets.only(
                                                right: 10),
                                            child: InkWell(
                                              onTap: () {
                                                showDialog(
                                                    context: context,
                                                    builder:
                                                        (BuildContext context) {
                                                      TextEditingController
                                                          replayController =
                                                          TextEditingController();
                                                      return AlertDialog(
                                                        actions: [
                                                          Form(
                                                            child: Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .center,
                                                              children: [
                                                                Flexible(
                                                                  child:
                                                                      Container(
                                                                    color: Colors
                                                                        .white,
                                                                    width: MediaQuery.of(context)
                                                                            .size
                                                                            .width *
                                                                        0.6,
                                                                    child: defaultFormField(
                                                                        prefixIcon:
                                                                            const Icon(Icons
                                                                                .comment),
                                                                        controller:
                                                                            replayController,
                                                                        type: TextInputType
                                                                            .text),
                                                                  ),
                                                                ),
                                                                defaultIconButton(
                                                                    onPressed:
                                                                        () {
                                                                      cubit.sendReplay(
                                                                          outlookId: cubit
                                                                              .posts[
                                                                                  postIndex]
                                                                              .id,
                                                                          commentId: cubit
                                                                              .posts[
                                                                                  postIndex]
                                                                              .comments[
                                                                                  ind]
                                                                              .id
                                                                              .toInt(),
                                                                          reply: replayController
                                                                              .text,
                                                                          context:
                                                                              context);
                                                                      Navigator.pop(
                                                                          context);
                                                                    },
                                                                    icon: Icons
                                                                        .send,
                                                                    color:
                                                                        mainColor)
                                                              ],
                                                            ),
                                                          )
                                                        ],
                                                      );
                                                    });
                                              },
                                              child: const Text(
                                                "رد",
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                cubit.post[postIndex].comments[ind].reply != ""
                                    ? Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(
                                            width: 50,
                                          ),
                                          Container(
                                            height: 40,
                                            width: 40,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(200)),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(200),
                                              child: Image.asset(
                                                  "images/icon.png"),
                                            ),
                                          ),
                                          Flexible(
                                            fit: FlexFit.tight,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                    margin:
                                                        const EdgeInsets.only(
                                                            right: 8),
                                                    padding:
                                                        const EdgeInsets.all(
                                                            4),
                                                    decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    10)),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text("ادمن",
                                                            style: TextStyle(
                                                                fontSize: 20,
                                                                color: Color
                                                                    .fromRGBO(
                                                                        66,
                                                                        105,
                                                                        129,
                                                                        1))),
                                                        Text(
                                                            cubit.post[postIndex].comments[ind].reply,
                                                            style: const TextStyle(
                                                                color: Colors
                                                                    .black,
                                                                fontSize:
                                                                    18)),
                                                      ],
                                                    )),
                                                const SizedBox(
                                                  height: 8,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      )
                                    : const SizedBox(),
                              ],
                            );
                          }),
                    ),
                  )
                : const SizedBox(),
            commentsAndReplay.isNotEmpty && cubit.profile?.role != "admin"
                ? Flexible(
                    flex: 1,
                    child: Container(
                      margin: const EdgeInsets.only(top: 10, bottom: 10),
                      child: ListView.builder(
                          physics: const BouncingScrollPhysics(),
                          controller: scrollController,
                          itemCount: commentsAndReplay.length,
                          itemBuilder: (_, index) {
                            return Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: 30,
                                      width: 30,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(200)),
                                      child: ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(200),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(200),
                                          child: cubit.profile?.pic != null &&
                                                  cubit.profile?.pic != ""
                                              ? CircleAvatar(
                                                  radius: 100,
                                                  backgroundImage:
                                                      CachedNetworkImageProvider(
                                                          "https://admin.rain-app.com/storage/users/${cubit.profile?.pic}"))
                                              : Image.asset(
                                                  "images/avatar.png"),
                                        ),
                                      ),
                                    ),
                                    Flexible(
                                      fit: FlexFit.tight,
                                      child: Column(
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        children: [
                                      Container(
                                          margin: const EdgeInsets.only(
                                              right: 8),
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      10)),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                      commentsAndReplay[
                                                              index]
                                                          .user
                                                          .name,
                                                      style:
                                                          const TextStyle(
                                                              fontSize: 14,
                                                              color: Color
                                                                  .fromRGBO(
                                                                      66,
                                                                      105,
                                                                      129,
                                                                      1))),
                                                  const SizedBox(
                                                    width: 8,
                                                  ),
                                                  Text(
                                                      intl.DateFormat.yMd()
                                                          .format(DateTime.parse(
                                                              commentsAndReplay[
                                                                      index]
                                                                  .date)),
                                                      style:
                                                          const TextStyle(
                                                              color: Colors
                                                                  .grey,
                                                              fontSize:
                                                                  12)),
                                                ],
                                              ),
                                              Text(
                                                  commentsAndReplay[index]
                                                      .comment,
                                                  style: const TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 14)),
                                            ],
                                          )),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 12,
                                ),
                                if (commentsAndReplay[index].reply == "") const SizedBox(
                                        height: 1,
                                      ) else Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(
                                            width: 50,
                                          ),
                                          Container(
                                            height: 40,
                                            width: 40,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(200)),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(200),
                                              child: Image.asset(
                                                  "images/icon.png"),
                                            ),
                                          ),
                                          Flexible(
                                            fit: FlexFit.tight,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                    margin:
                                                        const EdgeInsets.only(
                                                            right: 8),
                                                    padding:
                                                        const EdgeInsets.all(
                                                            4),
                                                    decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    10)),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text("ادمن",
                                                            style: TextStyle(
                                                                fontSize: 20,
                                                                color: Color
                                                                    .fromRGBO(
                                                                        66,
                                                                        105,
                                                                        129,
                                                                        1))),
                                                        Text(
                                                            commentsAndReplay[index].reply,
                                                            style: const TextStyle(
                                                                color: Colors
                                                                    .black,
                                                                fontSize:
                                                                    18)),
                                                      ],
                                                    )),
                                                const SizedBox(
                                                  height: 8,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      )
                              ],
                            );
                          }),
                    ),
                  )
                : const SizedBox(),
            cubit.profile?.role != "admin"
                ? Flexible(
                    flex: 0,
                    child: StatefulBuilder(builder: (context, setState) {
                      return TextField(
                        controller: commentController,
                        onChanged: (value) {
                          if (value.length == 1) {
                            setState(() => commentEntered = true);
                          }

                          if (value.isEmpty) {
                            setState(() => commentEntered = false);
                          }
                        },
                        decoration: InputDecoration(
                            contentPadding:
                                const EdgeInsets.symmetric(vertical: 10),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            hintText: "اكتب تعليق ..",
                            prefixIcon: GestureDetector(
                              onTap: commentEntered
                                  ? () {
                                      if (CacheHelper.getData(key: "login") !=
                                          null) {
                                        cubit.profile?.sub != true
                                            ? cubit.admobInitSt.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {  }

                                        )
                                            : const SizedBox();
                                        cubit.addComment(
                                            postIndex,
                                            commentController.text,
                                            cubit.posts[postIndex].id,
                                            cubit.profile!.id);
                                        cubit.sendComment(
                                            outlookId:
                                                cubit.posts[postIndex].id,
                                            comment: commentController.text);
                                      } else {
                                        LogInDialog(context);
                                      }
                                    }
                                  : null,
                              child: Icon(
                                Icons.send,
                                color: commentEntered
                                    ? const Color.fromRGBO(66, 105, 129, 1)
                                    : Colors.grey,
                                textDirection: TextDirection.ltr,
                              ),
                            )),
                      );
                    }),
                  )
                : const SizedBox()
          ],
        ),
      ),
    );
  }
}
